import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Calendar, Users, Settings, Menu, X, Heart, BarChart3 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";

const Navigation = () => {
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const location = useLocation();

  const isAdmin = location.pathname.startsWith("/admin");

  const publicNavItems = [
    { href: "/", label: "Agendar Atendimento", icon: Calendar },
    { href: "/sobre", label: "Sobre", icon: Heart },
  ];

  const adminNavItems = [
    { href: "/admin", label: "Dashboard", icon: Calendar },
    { href: "/admin/patients", label: "Clientes", icon: Users },
    { href: "/admin/reports", label: "<PERSON><PERSON><PERSON><PERSON><PERSON>", icon: BarChart3 },
    { href: "/admin/settings", label: "Configurações", icon: Settings },
  ];

  const navItems = isAdmin ? adminNavItems : publicNavItems;

  const NavContent = ({ mobile = false }) => (
    <div className={`flex ${mobile ? 'flex-col space-y-4' : 'items-center space-x-8'}`}>
      <Link 
        to="/" 
        className="flex items-center space-x-2 text-xl font-bold text-primary"
        onClick={() => mobile && setIsMobileOpen(false)}
      >
        <Heart className="h-6 w-6" />
        <span>Aptor</span>
      </Link>

      <nav className={`flex ${mobile ? 'flex-col space-y-4' : 'items-center space-x-6'}`}>
        {navItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.href;
          
          return (
            <Link
              key={item.href}
              to={item.href}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all ${
                isActive 
                  ? 'bg-primary text-primary-foreground shadow-md' 
                  : 'text-muted-foreground hover:text-primary hover:bg-secondary'
              }`}
              onClick={() => mobile && setIsMobileOpen(false)}
            >
              <Icon className="h-4 w-4" />
              <span>{item.label}</span>
            </Link>
          );
        })}
      </nav>

      {isAdmin && (
        <div className={`flex ${mobile ? 'flex-col space-y-2' : 'items-center space-x-4'}`}>
          <Button 
            variant="outline" 
            asChild
            onClick={() => mobile && setIsMobileOpen(false)}
          >
            <Link to="/">Área Pública</Link>
          </Button>
          <Button 
            variant="destructive" 
            size="sm"
            onClick={() => mobile && setIsMobileOpen(false)}
          >
            Sair
          </Button>
        </div>
      )}

      {!isAdmin && (
        <Button 
          asChild 
          className="bg-gradient-primary shadow-elegant"
          onClick={() => mobile && setIsMobileOpen(false)}
        >
          <Link to="/admin">Área do Profissional</Link>
        </Button>
      )}
    </div>
  );

  return (
    <header className="sticky top-0 z-50 w-full bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Desktop Navigation */}
          <div className="hidden md:flex w-full">
            <NavContent />
          </div>

          {/* Mobile Navigation */}
          <div className="flex md:hidden w-full items-center justify-between">
            <Link to="/" className="flex items-center space-x-2 text-xl font-bold text-primary">
              <Heart className="h-6 w-6" />
              <span>Aptor</span>
            </Link>

            <Sheet open={isMobileOpen} onOpenChange={setIsMobileOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <div className="flex flex-col h-full">
                  <div className="flex items-center justify-between mb-8">
                    <h2 className="text-lg font-semibold">Menu</h2>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => setIsMobileOpen(false)}
                    >
                      <X className="h-5 w-5" />
                    </Button>
                  </div>
                  <NavContent mobile />
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Navigation;