-- ===========================================
-- APTOR - SCRIPT 5: MANUTENÇÃO E LIMPEZA
-- Execute este SQL PERIODICAMENTE (semanal/mensal)
-- ===========================================

-- ===========================================
-- LIMPEZA AUTOMÁTICA
-- ===========================================

-- Limpar sessões expiradas
SELECT cleanup_expired_sessions() as sessoes_removidas;

-- Limpar tokens de verificação expirados
SELECT cleanup_expired_tokens() as tokens_removidos;

-- Limpar logs de auditoria antigos (mais de 6 meses)
DELETE FROM audit_logs 
WHERE created_at < NOW() - INTERVAL '6 months'
AND action NOT IN ('CREATE_SUPERADMIN', 'DELETE', 'DEACTIVATE');

-- ===========================================
-- ESTATÍSTICAS DO SISTEMA
-- ===========================================

-- Estatísticas gerais de usuários
SELECT 
    'Estatísticas de Usuários' as categoria,
    up.role,
    up.is_active,
    COUNT(*) as total,
    MIN(u.created_at) as primeiro_usuario,
    MAX(u.created_at) as ultimo_usuario
FROM users u
JOIN user_profiles up ON u.id = up.user_id
GROUP BY up.role, up.is_active
ORDER BY up.role, up.is_active;

-- Usuários por mês de criação
SELECT 
    'Usuários por Mês' as categoria,
    DATE_TRUNC('month', u.created_at) as mes,
    COUNT(*) as novos_usuarios
FROM users u
WHERE u.created_at >= NOW() - INTERVAL '12 months'
GROUP BY DATE_TRUNC('month', u.created_at)
ORDER BY mes DESC;

-- Últimos logins
SELECT 
    'Últimos Logins' as categoria,
    u.email,
    up.role,
    up.last_login,
    CASE 
        WHEN up.last_login IS NULL THEN 'Nunca logou'
        WHEN up.last_login < NOW() - INTERVAL '30 days' THEN 'Inativo (30+ dias)'
        WHEN up.last_login < NOW() - INTERVAL '7 days' THEN 'Pouco ativo (7+ dias)'
        ELSE 'Ativo'
    END as status_atividade
FROM users u
JOIN user_profiles up ON u.id = up.user_id
WHERE up.is_active = true
ORDER BY up.last_login DESC NULLS LAST;

-- Estatísticas de sessões
SELECT 
    'Estatísticas de Sessões' as categoria,
    COUNT(*) as total_sessoes,
    COUNT(CASE WHEN expires_at > NOW() THEN 1 END) as sessoes_ativas,
    COUNT(CASE WHEN expires_at <= NOW() THEN 1 END) as sessoes_expiradas,
    MIN(created_at) as primeira_sessao,
    MAX(created_at) as ultima_sessao
FROM sessions;

-- Top 10 ações de auditoria
SELECT 
    'Top Ações de Auditoria' as categoria,
    action,
    resource,
    COUNT(*) as total_ocorrencias,
    MAX(created_at) as ultima_ocorrencia
FROM audit_logs
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY action, resource
ORDER BY total_ocorrencias DESC
LIMIT 10;

-- ===========================================
-- VERIFICAÇÕES DE INTEGRIDADE
-- ===========================================

-- Verificar usuários sem perfil
SELECT 
    'Usuários sem Perfil' as problema,
    u.id,
    u.email,
    u.name,
    u.created_at
FROM users u
LEFT JOIN user_profiles up ON u.id = up.user_id
WHERE up.user_id IS NULL;

-- Verificar perfis sem usuário (órfãos)
SELECT 
    'Perfis Órfãos' as problema,
    up.id,
    up.user_id,
    up.role,
    up.created_at
FROM user_profiles up
LEFT JOIN users u ON up.user_id = u.id
WHERE u.id IS NULL;

-- Verificar contas sem usuário
SELECT 
    'Contas Órfãs' as problema,
    a.id,
    a.user_id,
    a.account_id,
    a.provider_id
FROM accounts a
LEFT JOIN users u ON a.user_id = u.id
WHERE u.id IS NULL;

-- Verificar sessões sem usuário
SELECT 
    'Sessões Órfãs' as problema,
    s.id,
    s.user_id,
    s.session_token,
    s.expires_at
FROM sessions s
LEFT JOIN users u ON s.user_id = u.id
WHERE u.id IS NULL;

-- ===========================================
-- OTIMIZAÇÃO DE PERFORMANCE
-- ===========================================

-- Analisar tabelas para otimizar queries
ANALYZE users;
ANALYZE accounts;
ANALYZE sessions;
ANALYZE verification_tokens;
ANALYZE user_profiles;
ANALYZE audit_logs;

-- Verificar tamanho das tabelas
SELECT 
    'Tamanho das Tabelas' as categoria,
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as tamanho_total,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as tamanho_dados,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as tamanho_indices
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('users', 'accounts', 'sessions', 'verification_tokens', 'user_profiles', 'audit_logs')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Verificar índices não utilizados (execute apenas se necessário)
/*
SELECT 
    'Índices Não Utilizados' as categoria,
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
AND idx_scan = 0
ORDER BY schemaname, tablename, indexname;
*/

-- ===========================================
-- BACKUP DE CONFIGURAÇÕES
-- ===========================================

-- Backup das configurações de roles
CREATE TEMP TABLE backup_roles AS
SELECT 
    u.email,
    up.role,
    up.is_active,
    up.specialty,
    up.deactivated_reason,
    u.created_at,
    up.updated_at
FROM users u
JOIN user_profiles up ON u.id = up.user_id
ORDER BY up.role, u.email;

-- Mostrar backup
SELECT 'Backup de Roles' as categoria, * FROM backup_roles;

-- ===========================================
-- RELATÓRIO FINAL
-- ===========================================

SELECT 
    'RELATÓRIO DE MANUTENÇÃO CONCLUÍDO' as status,
    NOW() as executado_em,
    (SELECT COUNT(*) FROM users) as total_usuarios,
    (SELECT COUNT(*) FROM user_profiles WHERE is_active = true) as usuarios_ativos,
    (SELECT COUNT(*) FROM sessions WHERE expires_at > NOW()) as sessoes_ativas,
    (SELECT COUNT(*) FROM audit_logs WHERE created_at >= NOW() - INTERVAL '24 hours') as logs_ultimas_24h;
