-- ===========================================
-- APTOR - SCRIPT 3: SEGURANÇA E RLS (CORRIGIDO)
-- Execute este SQL TERCEIRO no Supabase SQL Editor
-- ===========================================

-- ===========================================
-- HABILITAR ROW LEVEL SECURITY (RLS)
-- ===========================================

-- Habilitar RLS em todas as tabelas
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE verification_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- ===========================================
-- FUNÇÕES DE SEGURANÇA (CRIAR PRIMEIRO)
-- ===========================================

-- Função para verificar se usuário é SuperAdmin
CREATE OR REPLACE FUNCTION is_superadmin(user_uuid UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM user_profiles 
        WHERE user_id = user_uuid 
        AND role = 'superadmin' 
        AND is_active = true
    );
END;
$$ language 'plpgsql' SECURITY DEFINER;

-- Função para verificar se usuário está ativo
CREATE OR REPLACE FUNCTION is_user_active(user_uuid UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM user_profiles 
        WHERE user_id = user_uuid 
        AND is_active = true
    );
END;
$$ language 'plpgsql' SECURITY DEFINER;

-- Função para obter role do usuário
CREATE OR REPLACE FUNCTION get_user_role(user_uuid UUID DEFAULT auth.uid())
RETURNS TEXT AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role 
    FROM user_profiles 
    WHERE user_id = user_uuid;
    
    RETURN COALESCE(user_role, 'inactive');
END;
$$ language 'plpgsql' SECURITY DEFINER;

-- ===========================================
-- POLÍTICAS DE SEGURANÇA - USERS
-- ===========================================

-- Limpar políticas existentes
DROP POLICY IF EXISTS "users_select_own" ON users;
DROP POLICY IF EXISTS "users_update_own" ON users;
DROP POLICY IF EXISTS "superadmin_select_all_users" ON users;
DROP POLICY IF EXISTS "superadmin_insert_users" ON users;
DROP POLICY IF EXISTS "superadmin_update_all_users" ON users;

-- Usuários podem ver seu próprio perfil
CREATE POLICY "users_select_own" ON users
    FOR SELECT USING (auth.uid()::text = id::text);

-- Usuários podem atualizar seu próprio perfil
CREATE POLICY "users_update_own" ON users
    FOR UPDATE USING (auth.uid()::text = id::text);

-- SuperAdmins podem ver todos os usuários
CREATE POLICY "superadmin_select_all_users" ON users
    FOR SELECT USING (is_superadmin());

-- SuperAdmins podem inserir novos usuários
CREATE POLICY "superadmin_insert_users" ON users
    FOR INSERT WITH CHECK (is_superadmin());

-- SuperAdmins podem atualizar qualquer usuário
CREATE POLICY "superadmin_update_all_users" ON users
    FOR UPDATE USING (is_superadmin());

-- ===========================================
-- POLÍTICAS DE SEGURANÇA - ACCOUNTS
-- ===========================================

-- Limpar políticas existentes
DROP POLICY IF EXISTS "accounts_select_own" ON accounts;
DROP POLICY IF EXISTS "accounts_update_own" ON accounts;
DROP POLICY IF EXISTS "superadmin_select_all_accounts" ON accounts;
DROP POLICY IF EXISTS "superadmin_insert_accounts" ON accounts;
DROP POLICY IF EXISTS "superadmin_update_all_accounts" ON accounts;

-- Usuários podem ver suas próprias contas
CREATE POLICY "accounts_select_own" ON accounts
    FOR SELECT USING (auth.uid()::text = user_id::text);

-- Usuários podem atualizar suas próprias contas
CREATE POLICY "accounts_update_own" ON accounts
    FOR UPDATE USING (auth.uid()::text = user_id::text);

-- SuperAdmins podem ver todas as contas
CREATE POLICY "superadmin_select_all_accounts" ON accounts
    FOR SELECT USING (is_superadmin());

-- SuperAdmins podem inserir contas para novos usuários
CREATE POLICY "superadmin_insert_accounts" ON accounts
    FOR INSERT WITH CHECK (is_superadmin());

-- SuperAdmins podem atualizar qualquer conta
CREATE POLICY "superadmin_update_all_accounts" ON accounts
    FOR UPDATE USING (is_superadmin());

-- ===========================================
-- POLÍTICAS DE SEGURANÇA - SESSIONS
-- ===========================================

-- Limpar políticas existentes
DROP POLICY IF EXISTS "sessions_select_own" ON sessions;
DROP POLICY IF EXISTS "sessions_insert_own" ON sessions;
DROP POLICY IF EXISTS "sessions_delete_own" ON sessions;
DROP POLICY IF EXISTS "superadmin_select_all_sessions" ON sessions;
DROP POLICY IF EXISTS "superadmin_delete_all_sessions" ON sessions;

-- Usuários podem ver suas próprias sessões
CREATE POLICY "sessions_select_own" ON sessions
    FOR SELECT USING (auth.uid()::text = user_id::text);

-- Usuários podem inserir suas próprias sessões
CREATE POLICY "sessions_insert_own" ON sessions
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

-- Usuários podem deletar suas próprias sessões (logout)
CREATE POLICY "sessions_delete_own" ON sessions
    FOR DELETE USING (auth.uid()::text = user_id::text);

-- SuperAdmins podem ver todas as sessões
CREATE POLICY "superadmin_select_all_sessions" ON sessions
    FOR SELECT USING (is_superadmin());

-- SuperAdmins podem deletar qualquer sessão
CREATE POLICY "superadmin_delete_all_sessions" ON sessions
    FOR DELETE USING (is_superadmin());

-- ===========================================
-- POLÍTICAS DE SEGURANÇA - USER_PROFILES
-- ===========================================

-- Limpar políticas existentes
DROP POLICY IF EXISTS "user_profiles_select_own" ON user_profiles;
DROP POLICY IF EXISTS "user_profiles_update_own" ON user_profiles;
DROP POLICY IF EXISTS "superadmin_select_all_profiles" ON user_profiles;
DROP POLICY IF EXISTS "superadmin_insert_profiles" ON user_profiles;
DROP POLICY IF EXISTS "superadmin_update_all_profiles" ON user_profiles;
DROP POLICY IF EXISTS "superadmin_delete_profiles" ON user_profiles;

-- Usuários podem ver seu próprio perfil
CREATE POLICY "user_profiles_select_own" ON user_profiles
    FOR SELECT USING (auth.uid()::text = user_id::text);

-- Usuários podem atualizar seu próprio perfil (campos básicos)
CREATE POLICY "user_profiles_update_own" ON user_profiles
    FOR UPDATE USING (auth.uid()::text = user_id::text)
    WITH CHECK (auth.uid()::text = user_id::text);

-- SuperAdmins podem ver todos os perfis
CREATE POLICY "superadmin_select_all_profiles" ON user_profiles
    FOR SELECT USING (is_superadmin());

-- SuperAdmins podem inserir novos perfis
CREATE POLICY "superadmin_insert_profiles" ON user_profiles
    FOR INSERT WITH CHECK (is_superadmin());

-- SuperAdmins podem atualizar qualquer perfil
CREATE POLICY "superadmin_update_all_profiles" ON user_profiles
    FOR UPDATE USING (is_superadmin());

-- SuperAdmins podem deletar perfis (exceto o próprio)
CREATE POLICY "superadmin_delete_profiles" ON user_profiles
    FOR DELETE USING (
        is_superadmin()
        AND user_id != auth.uid()
    );

-- ===========================================
-- POLÍTICAS DE SEGURANÇA - AUDIT_LOGS
-- ===========================================

-- Limpar políticas existentes
DROP POLICY IF EXISTS "audit_logs_select_own" ON audit_logs;
DROP POLICY IF EXISTS "superadmin_select_all_logs" ON audit_logs;
DROP POLICY IF EXISTS "system_insert_logs" ON audit_logs;

-- Usuários podem ver logs relacionados a eles
CREATE POLICY "audit_logs_select_own" ON audit_logs
    FOR SELECT USING (
        auth.uid()::text = user_id::text
        OR auth.uid()::text = resource_id
    );

-- SuperAdmins podem ver todos os logs
CREATE POLICY "superadmin_select_all_logs" ON audit_logs
    FOR SELECT USING (is_superadmin());

-- Sistema pode inserir logs (via triggers e aplicação)
CREATE POLICY "system_insert_logs" ON audit_logs
    FOR INSERT WITH CHECK (true);

-- ===========================================
-- POLÍTICAS DE SEGURANÇA - VERIFICATION_TOKENS
-- ===========================================

-- Limpar políticas existentes
DROP POLICY IF EXISTS "system_manage_tokens" ON verification_tokens;

-- Sistema pode gerenciar tokens de verificação
CREATE POLICY "system_manage_tokens" ON verification_tokens
    FOR ALL USING (true);

-- ===========================================
-- TRIGGERS PARA AUDITORIA
-- ===========================================

-- Função para log de auditoria em user_profiles
CREATE OR REPLACE FUNCTION log_user_profile_changes()
RETURNS TRIGGER AS $$
BEGIN
    -- Log para UPDATE
    IF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_logs (
            user_id,
            action,
            resource,
            resource_id,
            old_values,
            new_values
        ) VALUES (
            COALESCE(NEW.updated_by, OLD.user_id),
            'UPDATE',
            'user_profiles',
            OLD.user_id::text,
            to_jsonb(OLD),
            to_jsonb(NEW)
        );
        RETURN NEW;
    END IF;
    
    -- Log para INSERT
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_logs (
            user_id,
            action,
            resource,
            resource_id,
            new_values
        ) VALUES (
            COALESCE(NEW.created_by, NEW.user_id),
            'INSERT',
            'user_profiles',
            NEW.user_id::text,
            to_jsonb(NEW)
        );
        RETURN NEW;
    END IF;
    
    -- Log para DELETE
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit_logs (
            user_id,
            action,
            resource,
            resource_id,
            old_values
        ) VALUES (
            OLD.user_id,
            'DELETE',
            'user_profiles',
            OLD.user_id::text,
            to_jsonb(OLD)
        );
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ language 'plpgsql';

-- Trigger para auditoria de user_profiles (com proteção contra duplicação)
DROP TRIGGER IF EXISTS user_profiles_audit_trigger ON user_profiles;
CREATE TRIGGER user_profiles_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION log_user_profile_changes();

-- ===========================================
-- FUNÇÕES UTILITÁRIAS
-- ===========================================

-- Função para limpar sessões expiradas
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM sessions WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    INSERT INTO audit_logs (action, resource, new_values)
    VALUES ('CLEANUP', 'sessions', jsonb_build_object('deleted_count', deleted_count));
    
    RETURN deleted_count;
END;
$$ language 'plpgsql';

-- Função para limpar tokens de verificação expirados
CREATE OR REPLACE FUNCTION cleanup_expired_tokens()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM verification_tokens WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    INSERT INTO audit_logs (action, resource, new_values)
    VALUES ('CLEANUP', 'verification_tokens', jsonb_build_object('deleted_count', deleted_count));
    
    RETURN deleted_count;
END;
$$ language 'plpgsql';

-- ===========================================
-- VERIFICAR POLÍTICAS CRIADAS
-- ===========================================

-- Listar todas as políticas criadas
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    cmd
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- Mostrar resultado
SELECT 'Políticas de segurança criadas com sucesso!' as status;
