project_id = "kpqxdptknmpsoswswrpd"

[api]
enabled = true
port = 54321
schemas = ["public", "storage", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[db]
port = 54322

[db.pooler]
enabled = false
port = 54329
pool_mode = "transaction"
default_pool_size = 20
max_client_conn = 100

[realtime]
enabled = true
ip_version = "ipv4"
port = 54323
max_header_length = 4096

[studio]
enabled = true
port = 54324
api_url = "http://127.0.0.1:54321"
openai_api_key = "env(OPENAI_API_KEY)"

[inbucket]
enabled = true
port = 54325
smtp_port = 54326
pop3_port = 54327

[storage]
enabled = true
file_size_limit = "50MiB"
image_transformation.enabled = true

[auth]
enabled = true
site_url = "http://127.0.0.1:3000"
additional_redirect_urls = ["https://127.0.0.1:3000"]
jwt_expiry = 3600
enable_signup = true
enable_email_confirmations = false
enable_email_change_confirmations = true
enable_phone_confirmations = false
enable_phone_change_confirmations = true

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false
secure_email_change_enabled = true
smtp_admin_email = "<EMAIL>"
smtp_host = "127.0.0.1"
smtp_port = 54326
smtp_user = "fake_mail_user"
smtp_pass = "fake_mail_password"
smtp_sender_name = "fake_sender"

[auth.sms]
enable_signup = true
enable_confirmations = true
secure_phone_change_enabled = true
template = "Your code is {{ .Code }} ."
test_oto = [
  { phone = "+12345678901", code = "123456" },
]

[edge_runtime]
enabled = true
ip_version = "ipv4"
port = 54326
inspector_port = 8083

[functions.send-email-notification]
verify_jwt = false