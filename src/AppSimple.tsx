import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import TestPage from "./pages/TestPage";

const queryClient = new QueryClient();

const AppSimple = () => (
  <QueryClientProvider client={queryClient}>
    <BrowserRouter>
      <div className="min-h-screen bg-background">
        <Routes>
          <Route path="/" element={<TestPage />} />
          <Route path="/test" element={<TestPage />} />
          <Route path="*" element={<div>Página não encontrada</div>} />
        </Routes>
      </div>
    </BrowserRouter>
  </QueryClientProvider>
);

export default AppSimple;
