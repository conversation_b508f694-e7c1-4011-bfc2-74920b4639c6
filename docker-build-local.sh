#!/bin/bash

# ===========================================
# APTOR - BUILD LOCAL (MacOS)
# Para desenvolvimento e testes locais
# ===========================================

set -e

# Verificar se foi passado um nome para a imagem
if [ -z "$1" ]; then
    echo "❌ Erro: Nome da imagem não fornecido"
    echo "📋 Uso: ./docker-build-local.sh <nome-da-imagem>"
    echo "📋 Exemplo: ./docker-build-local.sh aptor-local-v1.0.0"
    exit 1
fi

IMAGE_NAME="datanerd/automation:$1"

echo "🚀 Iniciando build local para MacOS..."
echo "📦 Imagem: $IMAGE_NAME"
echo "🏗️  Arquitetura: $(uname -m)"
echo "💻 Sistema: $(uname -s)"

# Verificar se Docker está rodando
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker não está rodando. Inicie o Docker Desktop."
    exit 1
fi

# Build da imagem
echo "🔨 Construindo imagem..."
docker build \
    --platform linux/amd64 \
    -t "$IMAGE_NAME" \
    -f Dockerfile \
    .

if [ $? -eq 0 ]; then
    echo "✅ Build concluído com sucesso!"
    echo "📦 Imagem criada: $IMAGE_NAME"
    echo ""
    echo "🎯 Para testar localmente:"
    echo "   docker run -p 3001:3001 --env-file .env $IMAGE_NAME"
    echo ""
    echo "🚀 Para fazer push (opcional):"
    echo "   docker push $IMAGE_NAME"
else
    echo "❌ Erro no build!"
    exit 1
fi
