import { useState, useEffect } from "react";
import { Save, Clock, Calendar, User, Bell, Smartphone, Cloud, RefreshCw, Zap, Mail } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { profissionalService, horarioTrabalhoService } from "@/services/database";
import automationService from "@/services/automationService";
import { notifyDisponibilidadeAlterada } from "@/services/webhooks";
import { Badge } from "@/components/ui/badge";

const AdminSettings = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isSyncing, setIsSyncing] = useState(false);
  const [isTestingAutomation, setIsTestingAutomation] = useState(false);
  const [settings, setSettings] = useState({
    // Professional Info
    name: "",
    specialty: "",
    conselho_sigla: "",
    registro_conselho: "",
    email: "",
    phone: "",

    // Social Media & Contact
    instagram_url: "",
    facebook_url: "",
    linkedin_url: "",
    website_url: "",
    whatsapp_link: "",
    informacoes_importantes: "",

    // Working Hours
    appointmentDuration: "30",
    workingDays: {
      monday: { active: true, start: "08:00", end: "18:00" },
      tuesday: { active: true, start: "08:00", end: "18:00" },
      wednesday: { active: true, start: "08:00", end: "18:00" },
      thursday: { active: true, start: "08:00", end: "18:00" },
      friday: { active: true, start: "08:00", end: "17:00" },
      saturday: { active: false, start: "08:00", end: "12:00" },
      sunday: { active: false, start: "08:00", end: "12:00" }
    },

    // Notifications
    emailNotifications: true,
    smsNotifications: false,
    reminderTime: "24", // hours before

    // Google Calendar
    googleCalendarConnected: false,
    calendarId: "",

    // WhatsApp
    whatsappConnected: false,
    whatsappNumber: "",
  });

  // Fetch professional data
  const { data: professionals } = useQuery({
    queryKey: ['professionals'],
    queryFn: profissionalService.getAll
  });

  // Fetch work schedules
  const { data: workSchedules } = useQuery({
    queryKey: ['workSchedules'],
    queryFn: horarioTrabalhoService.getAll
  });

  // Load data when component mounts
  useEffect(() => {
    if (professionals && professionals.length > 0) {
      const professional = professionals[0];
      const redes_sociais = professional.redes_sociais || {};

      setSettings(prev => ({
        ...prev,
        name: professional.nome || "",
        specialty: professional.especialidade || "",
        conselho_sigla: professional.conselho_sigla || "",
        registro_conselho: professional.registro_conselho || professional.crm || "",
        email: professional.email || "",
        phone: professional.telefone || "",
        appointmentDuration: String(professional.duracao_consulta || 30),
        instagram_url: redes_sociais.instagram || "",
        facebook_url: redes_sociais.facebook || "",
        linkedin_url: redes_sociais.linkedin || "",
        website_url: redes_sociais.website || "",
        whatsapp_link: redes_sociais.whatsapp || "",
        informacoes_importantes: professional.informacoes_importantes || ""
      }));
    }
  }, [professionals]);

  useEffect(() => {
    if (workSchedules && workSchedules.length > 0) {
      const newWorkingDays = { ...settings.workingDays };
      
      workSchedules.forEach(schedule => {
        const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        const dayKey = dayNames[schedule.dia_semana] as keyof typeof newWorkingDays;
        
        newWorkingDays[dayKey] = {
          active: schedule.ativo || false,
          start: schedule.hora_inicio || "08:00",
          end: schedule.hora_fim || "18:00"
        };
      });

      setSettings(prev => ({
        ...prev,
        workingDays: newWorkingDays
      }));
    }
  }, [workSchedules]);

  // Mutations for saving data
  const createProfessionalMutation = useMutation({
    mutationFn: profissionalService.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['professionals'] });
    }
  });

  const updateProfessionalMutation = useMutation({
    mutationFn: ({ id, ...data }: { id: string } & any) => profissionalService.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['professionals'] });
    }
  });

  const createWorkScheduleMutation = useMutation({
    mutationFn: horarioTrabalhoService.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workSchedules'] });
    }
  });

  const updateWorkScheduleMutation = useMutation({
    mutationFn: ({ id, ...data }: { id: string } & any) => horarioTrabalhoService.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workSchedules'] });
    }
  });

  const handleSave = async () => {
    try {
      // Save professional data
      const professionalData = {
        nome: settings.name,
        especialidade: settings.specialty,
        conselho_sigla: settings.conselho_sigla,
        registro_conselho: settings.registro_conselho,
        email: settings.email,
        telefone: settings.phone,
        duracao_consulta: parseInt(settings.appointmentDuration),
        redes_sociais: {
          instagram: settings.instagram_url,
          facebook: settings.facebook_url,
          linkedin: settings.linkedin_url,
          website: settings.website_url,
          whatsapp: settings.whatsapp_link
        },
        informacoes_importantes: settings.informacoes_importantes
      };

      if (professionals && professionals.length > 0) {
        await updateProfessionalMutation.mutateAsync({ 
          id: professionals[0].id, 
          ...professionalData 
        });
      } else {
        await createProfessionalMutation.mutateAsync(professionalData);
      }

      // Save work schedules
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      
      for (const [dayKey, dayData] of Object.entries(settings.workingDays)) {
        const dayNumber = dayNames.indexOf(dayKey);
        const existingSchedule = workSchedules?.find(schedule => schedule.dia_semana === dayNumber);
        
        const scheduleData = {
          dia_semana: dayNumber,
          hora_inicio: dayData.start,
          hora_fim: dayData.end,
          ativo: dayData.active,
          profissional_id: professionals?.[0]?.id || null
        };

        if (existingSchedule) {
          await updateWorkScheduleMutation.mutateAsync({
            id: existingSchedule.id,
            ...scheduleData
          });
        } else {
          await createWorkScheduleMutation.mutateAsync(scheduleData);
        }
      }

      toast({
        title: "Configurações salvas!",
        description: "Suas configurações foram atualizadas com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro ao salvar",
        description: "Ocorreu um erro ao salvar as configurações. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  const handleForcedSync = async () => {
    setIsSyncing(true);

    try {
      // Create webhook payload for forced sync
      const syncPayload = {
        event_type: 'sync_calendar_forced',
        timestamp: new Date().toISOString(),
        data: {
          profissional_id: professionals?.[0]?.id || null,
          sync_type: 'full',
          requested_by: 'admin_panel'
        }
      };

      // Send webhook to N8N for calendar sync
      const response = await fetch('https://automation.datanerd.com.br/webhook/sync-calendar', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(syncPayload),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json().catch(() => ({}));

      console.log('Sync webhook sent successfully:', result);

      toast({
        title: "Sincronização iniciada!",
        description: "A sincronização com o Google Calendar foi iniciada. Pode levar alguns minutos para ser concluída.",
      });

    } catch (error) {
      console.error('Erro ao forçar sincronização:', error);
      toast({
        title: "Erro na sincronização",
        description: "Não foi possível iniciar a sincronização. Verifique sua conexão e tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSyncing(false);
    }
  };

  const handleTestAutomation = async (type: 'presence_check' | 'reminder_emails') => {
    setIsTestingAutomation(true);

    try {
      const result = await automationService.manualTrigger(type);

      if (result.success) {
        toast({
          title: "Teste executado com sucesso!",
          description: type === 'presence_check'
            ? "Verificação de presença executada. Verifique os logs para detalhes."
            : "Envio de lembretes executado. Verifique os emails enviados.",
        });
      } else {
        throw new Error(result.error || 'Erro desconhecido');
      }
    } catch (error) {
      console.error('Erro ao testar automação:', error);
      toast({
        title: "Erro no teste",
        description: "Não foi possível executar o teste. Verifique os logs para mais detalhes.",
        variant: "destructive"
      });
    } finally {
      setIsTestingAutomation(false);
    }
  };

  const handleWorkingDayChange = (day: string, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      workingDays: {
        ...prev.workingDays,
        [day]: {
          ...prev.workingDays[day as keyof typeof prev.workingDays],
          [field]: value
        }
      }
    }));
  };

  const handleToggleWorkingDay = async (day: string, checked: boolean) => {
    // Check if professional exists
    if (!professionals || professionals.length === 0) {
      toast({
        title: "Erro",
        description: "Salve as informações do profissional primeiro antes de configurar disponibilidade.",
        variant: "destructive"
      });
      return;
    }

    const professional = professionals[0];
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const diaSemana = dayNames.indexOf(day);
    
    // Find existing schedule for this day
    const existingSchedule = workSchedules?.find(schedule => schedule.dia_semana === diaSemana);
    const bloqueioEventId = existingSchedule?.bloqueio_event_id;

    try {
      // Update UI immediately
      handleWorkingDayChange(day, 'active', checked);

      // Send webhook
      const webhookResponse = await notifyDisponibilidadeAlterada(
        professional.id,
        diaSemana,
        checked,
        bloqueioEventId
      );

      if (webhookResponse?.success) {
        // Update database with returned event ID
        if (existingSchedule) {
          const updatedData = {
            ...existingSchedule,
            ativo: checked,
            bloqueio_event_id: checked ? null : webhookResponse.data?.event_id || bloqueioEventId
          };
          
          await updateWorkScheduleMutation.mutateAsync({
            id: existingSchedule.id,
            ...updatedData
          });
        }

        toast({
          title: checked ? "Dia ativado" : "Dia desativado",
          description: checked 
            ? "A agenda foi desbloqueada para este dia."
            : "A agenda foi bloqueada para este dia.",
        });

        console.log('Webhook response:', webhookResponse);
      } else {
        throw new Error(webhookResponse?.error || 'Falha no webhook');
      }
    } catch (error) {
      // Revert UI change on error
      handleWorkingDayChange(day, 'active', !checked);
      
      toast({
        title: "Erro ao alterar disponibilidade",
        description: "Não foi possível alterar a disponibilidade. Tente novamente.",
        variant: "destructive"
      });
      
      console.error('Erro ao alterar disponibilidade:', error);
    }
  };

  const dayNames = {
    monday: "Segunda-feira",
    tuesday: "Terça-feira", 
    wednesday: "Quarta-feira",
    thursday: "Quinta-feira",
    friday: "Sexta-feira",
    saturday: "Sábado",
    sunday: "Domingo"
  };

  return (
    <div className="min-h-screen bg-gradient-secondary">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 animate-fade-in-up">
          <div>
            <h1 className="text-3xl font-bold text-primary mb-2">Configurações</h1>
            <p className="text-muted-foreground">
              Configure sua conta e preferências do sistema
            </p>
          </div>
          <Button onClick={handleSave} className="bg-gradient-primary shadow-elegant mt-4 md:mt-0">
            <Save className="h-4 w-4 mr-2" />
            Salvar Configurações
          </Button>
        </div>

        <div className="grid gap-8 lg:grid-cols-2">
          {/* Professional Info */}
          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5 text-primary" />
                <span>Informações Profissionais</span>
              </CardTitle>
              <CardDescription>
                Dados do profissional exibidos para os clientes
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="name">Nome Completo</Label>
                  <Input
                    id="name"
                    value={settings.name}
                    onChange={(e) => setSettings(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="specialty">Especialidade</Label>
                  <Input
                    id="specialty"
                    value={settings.specialty}
                    onChange={(e) => setSettings(prev => ({ ...prev, specialty: e.target.value }))}
                  />
                </div>
              </div>
              
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="conselho">Conselho Profissional</Label>
                  <Select value={settings.conselho_sigla} onValueChange={(value) => setSettings(prev => ({ ...prev, conselho_sigla: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o conselho" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CRM">CRM</SelectItem>
                      <SelectItem value="CREF">CREF</SelectItem>
                      <SelectItem value="CRN">CRN</SelectItem>
                      <SelectItem value="CRO">CRO</SelectItem>
                      <SelectItem value="CRP">CRP</SelectItem>
                      <SelectItem value="CREFITO">CREFITO</SelectItem>
                      <SelectItem value="CREA">CREA</SelectItem>
                      <SelectItem value="CRC">CRC</SelectItem>
                      <SelectItem value="OAB">OAB</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="registro">Número do Registro</Label>
                  <Input
                    id="registro"
                    value={settings.registro_conselho}
                    onChange={(e) => setSettings(prev => ({ ...prev, registro_conselho: e.target.value }))}
                    placeholder="Digite o número do registro"
                  />
                </div>
                <div>
                  <Label htmlFor="phone">Telefone</Label>
                  <Input
                    id="phone"
                    value={settings.phone}
                    onChange={(e) => setSettings(prev => ({ ...prev, phone: e.target.value }))}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="email">E-mail</Label>
                <Input
                  id="email"
                  type="email"
                  value={settings.email}
                  onChange={(e) => setSettings(prev => ({ ...prev, email: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="informacoes">Informações Importantes</Label>
                <Textarea
                  id="informacoes"
                  value={settings.informacoes_importantes}
                  onChange={(e) => setSettings(prev => ({ ...prev, informacoes_importantes: e.target.value }))}
                  placeholder="Digite informações importantes para os clientes (uma por linha)"
                  rows={4}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Cada linha será exibida como um item na lista de informações importantes
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Social Media & Contact */}
          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Smartphone className="h-5 w-5 text-primary" />
                <span>Redes Sociais e Contato</span>
              </CardTitle>
              <CardDescription>
                Links para suas redes sociais e contatos adicionais
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="instagram">Instagram</Label>
                  <Input
                    id="instagram"
                    value={settings.instagram_url}
                    onChange={(e) => setSettings(prev => ({ ...prev, instagram_url: e.target.value }))}
                    placeholder="https://instagram.com/seu_perfil"
                  />
                </div>
                <div>
                  <Label htmlFor="facebook">Facebook</Label>
                  <Input
                    id="facebook"
                    value={settings.facebook_url}
                    onChange={(e) => setSettings(prev => ({ ...prev, facebook_url: e.target.value }))}
                    placeholder="https://facebook.com/seu_perfil"
                  />
                </div>
                <div>
                  <Label htmlFor="linkedin">LinkedIn</Label>
                  <Input
                    id="linkedin"
                    value={settings.linkedin_url}
                    onChange={(e) => setSettings(prev => ({ ...prev, linkedin_url: e.target.value }))}
                    placeholder="https://linkedin.com/in/seu_perfil"
                  />
                </div>
                <div>
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={settings.website_url}
                    onChange={(e) => setSettings(prev => ({ ...prev, website_url: e.target.value }))}
                    placeholder="https://seusite.com.br"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="whatsapp">Link do WhatsApp</Label>
                <Input
                  id="whatsapp"
                  value={settings.whatsapp_link}
                  onChange={(e) => setSettings(prev => ({ ...prev, whatsapp_link: e.target.value }))}
                  placeholder="https://wa.me/5511999999999"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Use o formato: https://wa.me/55DDNÚMERO (com código do país e DDD)
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Appointment Settings */}
          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-primary" />
                <span>Configurações de Atendimento</span>
              </CardTitle>
              <CardDescription>
                Defina a duração e configurações dos atendimentos
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="duration">Duração do Atendimento (minutos)</Label>
                <Select value={settings.appointmentDuration} onValueChange={(value) => setSettings(prev => ({ ...prev, appointmentDuration: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15 minutos</SelectItem>
                    <SelectItem value="30">30 minutos</SelectItem>
                    <SelectItem value="45">45 minutos</SelectItem>
                    <SelectItem value="60">60 minutos</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="reminder">Lembrete para Pacientes</Label>
                <Select value={settings.reminderTime} onValueChange={(value) => setSettings(prev => ({ ...prev, reminderTime: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 hora antes</SelectItem>
                    <SelectItem value="2">2 horas antes</SelectItem>
                    <SelectItem value="24">24 horas antes</SelectItem>
                    <SelectItem value="48">48 horas antes</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Working Hours */}
          <Card className="shadow-card lg:col-span-2 animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-5 w-5 text-primary" />
                <span>Horários de Funcionamento</span>
              </CardTitle>
              <CardDescription>
                Configure seus dias e horários de atendimento. Dias desativados ficam indisponíveis para marcação (bloqueio de agenda).
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(settings.workingDays).map(([day, config]) => (
                  <div 
                    key={day} 
                    className={`flex items-center justify-between p-4 rounded-lg border transition-all duration-200 ${
                      config.active 
                        ? "bg-gradient-card border-border" 
                        : "bg-muted/40 border-dashed border-muted-foreground/30 opacity-60"
                    }`}
                  >
                    <div className="flex items-center space-x-4">
                      <Switch
                        checked={config.active}
                        onCheckedChange={(checked) => handleToggleWorkingDay(day, checked)}
                        aria-label={`${config.active ? 'Desativar' : 'Ativar'} ${dayNames[day as keyof typeof dayNames]}`}
                      />
                      <div className="flex items-center space-x-3">
                        <Label className="font-medium min-w-[120px]">
                          {dayNames[day as keyof typeof dayNames]}
                        </Label>
                        {!config.active && (
                          <Badge variant="secondary" className="text-xs">
                            Desativado
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      {!config.active && (
                        <p className="text-sm text-muted-foreground">
                          Dia desativado — a agenda será bloqueada
                        </p>
                      )}
                      
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <Label className="text-sm">Das</Label>
                          <Input
                            type="time"
                            value={config.start}
                            onChange={(e) => handleWorkingDayChange(day, 'start', e.target.value)}
                            className="w-24"
                            disabled={!config.active}
                          />
                        </div>
                        <div className="flex items-center space-x-2">
                          <Label className="text-sm">às</Label>
                          <Input
                            type="time"
                            value={config.end}
                            onChange={(e) => handleWorkingDayChange(day, 'end', e.target.value)}
                            className="w-24"
                            disabled={!config.active}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Notifications */}
          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bell className="h-5 w-5 text-primary" />
                <span>Notificações</span>
              </CardTitle>
              <CardDescription>
                Configure como você deseja receber notificações
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Notificações por E-mail</Label>
                  <p className="text-sm text-muted-foreground">
                    Receba confirmações e lembretes por e-mail
                  </p>
                </div>
                <Switch
                  checked={settings.emailNotifications}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, emailNotifications: checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Notificações por SMS</Label>
                  <p className="text-sm text-muted-foreground">
                    Receba notificações via SMS
                  </p>
                </div>
                <Switch
                  checked={settings.smsNotifications}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, smsNotifications: checked }))}
                />
              </div>
            </CardContent>
          </Card>

          {/* Integrations */}
          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.5s' }}>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Cloud className="h-5 w-5 text-primary" />
                <span>Integrações</span>
              </CardTitle>
              <CardDescription>
                Conecte com Google Calendar e WhatsApp
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Google Calendar</Label>
                    <p className="text-sm text-muted-foreground">
                      Sincronize automaticamente com sua agenda
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={settings.googleCalendarConnected ? "bg-success text-success-foreground" : "bg-muted text-muted-foreground"}>
                      {settings.googleCalendarConnected ? "Conectado" : "Desconectado"}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleForcedSync}
                      disabled={isSyncing}
                    >
                      <RefreshCw className={`h-4 w-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
                      {isSyncing ? 'Sincronizando...' : 'Sincronizar Agora'}
                    </Button>
                  </div>
                </div>
                
                {settings.googleCalendarConnected && (
                  <div>
                    <Label htmlFor="calendarId">ID do Calendário</Label>
                    <Input
                      id="calendarId"
                      value={settings.calendarId}
                      onChange={(e) => setSettings(prev => ({ ...prev, calendarId: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                )}
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>WhatsApp Business</Label>
                    <p className="text-sm text-muted-foreground">
                      Envie confirmações via WhatsApp
                    </p>
                  </div>
                  <Button variant={settings.whatsappConnected ? "destructive" : "default"}>
                    {settings.whatsappConnected ? "Desconectar" : "Conectar"}
                  </Button>
                </div>
                
                {settings.whatsappConnected && (
                  <div>
                    <Label htmlFor="whatsappNumber">Número do WhatsApp</Label>
                    <Input
                      id="whatsappNumber"
                      value={settings.whatsappNumber}
                      onChange={(e) => setSettings(prev => ({ ...prev, whatsappNumber: e.target.value }))}
                      placeholder="(11) 99999-9999"
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Automation Settings */}
          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-primary" />
                <span>Automações</span>
              </CardTitle>
              <CardDescription>
                Teste e configure automações do sistema
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">Verificação de Presença</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Marca automaticamente como "ausente" clientes que não comparecem após 15 minutos
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestAutomation('presence_check')}
                    disabled={isTestingAutomation}
                  >
                    <Zap className={`h-4 w-4 mr-2 ${isTestingAutomation ? 'animate-pulse' : ''}`} />
                    {isTestingAutomation ? 'Testando...' : 'Testar Agora'}
                  </Button>
                </div>

                <div className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">Lembretes por Email</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Envia lembretes automáticos para clientes com agendamentos no dia seguinte
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestAutomation('reminder_emails')}
                    disabled={isTestingAutomation}
                  >
                    <Mail className={`h-4 w-4 mr-2 ${isTestingAutomation ? 'animate-pulse' : ''}`} />
                    {isTestingAutomation ? 'Testando...' : 'Testar Agora'}
                  </Button>
                </div>
              </div>

              <div className="bg-muted p-4 rounded-lg">
                <h4 className="font-medium mb-2">ℹ️ Informações sobre Automações</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Verificação de presença: Executa a cada 15 minutos automaticamente</li>
                  <li>• Lembretes por email: Executa diariamente às 18h</li>
                  <li>• Use os botões de teste para verificar o funcionamento</li>
                  <li>• As automações são gerenciadas via N8N</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Save Button */}
        <div className="flex justify-center mt-8">
          <Button onClick={handleSave} size="lg" className="bg-gradient-primary shadow-elegant animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
            <Save className="h-5 w-5 mr-2" />
            Salvar Todas as Configurações
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AdminSettings;