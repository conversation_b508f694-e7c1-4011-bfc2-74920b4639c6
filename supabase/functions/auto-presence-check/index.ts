import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('Starting auto presence check...');

    // Get current time
    const now = new Date();
    const fifteenMinutesAgo = new Date(now.getTime() - 15 * 60 * 1000);

    // Find appointments that should be marked as no-show
    // Criteria: appointment time + 15 minutes < now AND status is still 'agendado'
    const { data: appointmentsToCheck, error: appointmentsError } = await supabaseClient
      .from('agendamentos')
      .select(`
        id,
        data_hora,
        status,
        cliente_id,
        profissional_id,
        clientes (
          id,
          nome,
          email,
          presenca_status
        ),
        profissionais (
          id,
          nome,
          email
        )
      `)
      .eq('status', 'agendado')
      .lt('data_hora', fifteenMinutesAgo.toISOString());

    if (appointmentsError) {
      console.error('Error fetching appointments:', appointmentsError);
      throw appointmentsError;
    }

    console.log(`Found ${appointmentsToCheck?.length || 0} appointments to check`);

    let updatedCount = 0;
    let emailsSent = 0;

    if (appointmentsToCheck && appointmentsToCheck.length > 0) {
      for (const appointment of appointmentsToCheck) {
        try {
          // Update appointment status to 'ausente'
          const { error: updateAppointmentError } = await supabaseClient
            .from('agendamentos')
            .update({ status: 'ausente' })
            .eq('id', appointment.id);

          if (updateAppointmentError) {
            console.error(`Error updating appointment ${appointment.id}:`, updateAppointmentError);
            continue;
          }

          // Update client presence status to 'no_show'
          if (appointment.clientes) {
            const { error: updateClientError } = await supabaseClient
              .from('clientes')
              .update({ presenca_status: 'no_show' })
              .eq('id', appointment.cliente_id);

            if (updateClientError) {
              console.error(`Error updating client ${appointment.cliente_id}:`, updateClientError);
            }
          }

          updatedCount++;

          // Send notification email to professional if they have email
          if (appointment.profissionais?.email && appointment.clientes) {
            try {
              const appointmentDate = new Date(appointment.data_hora);
              const formattedDate = appointmentDate.toLocaleDateString('pt-BR', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              });
              const formattedTime = appointmentDate.toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit'
              });

              // Call email notification function
              const { error: emailError } = await supabaseClient.functions.invoke('send-email-notification', {
                body: {
                  to: appointment.profissionais.email,
                  clientName: appointment.clientes.nome,
                  professionalName: appointment.profissionais.nome,
                  appointmentDate: formattedDate,
                  appointmentTime: formattedTime,
                  type: 'no_show_alert'
                }
              });

              if (emailError) {
                console.error(`Error sending no-show email for appointment ${appointment.id}:`, emailError);
              } else {
                emailsSent++;
                console.log(`No-show alert email sent for appointment ${appointment.id}`);
              }
            } catch (emailError) {
              console.error(`Error sending no-show email for appointment ${appointment.id}:`, emailError);
            }
          }

          console.log(`Updated appointment ${appointment.id} to 'ausente' status`);
        } catch (error) {
          console.error(`Error processing appointment ${appointment.id}:`, error);
        }
      }
    }

    // Log results
    console.log(`Auto presence check completed. Updated ${updatedCount} appointments, sent ${emailsSent} emails.`);

    return new Response(
      JSON.stringify({
        success: true,
        message: `Auto presence check completed`,
        appointmentsChecked: appointmentsToCheck?.length || 0,
        appointmentsUpdated: updatedCount,
        emailsSent: emailsSent,
        timestamp: now.toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Error in auto presence check:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
})
