import { betterAuth } from 'better-auth';
import { Redis } from 'ioredis';
import { createClient } from '@supabase/supabase-js';

// Redis Cloud para autenticação (DB 1)
const authRedis = new Redis({
  host: process.env.REDIS_HOST || 'redis-14154.c308.sa-east-1-1.ec2.redns.redis-cloud.com',
  port: parseInt(process.env.REDIS_PORT || '14154'),
  username: process.env.REDIS_USERNAME || 'hordak',
  password: process.env.REDIS_PASSWORD || '3B4q!t#T8xuvPDf',
  db: 1, // DB separado para auth
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: 3,
  connectTimeout: 10000,
  lazyConnect: true,
});

// Supabase para persistência de usuários
const supabase = createClient(
  process.env.SUPABASE_URL || 'https://kpqxdptknmpsoswswrpd.supabase.co',
  process.env.SUPABASE_SERVICE_KEY || 'sb_secret_nxwQu2ggElko3t1iPvy6YQ_yYulToEw'
);

// Configuração do Better Auth
export const auth = betterAuth({
  // Database - Supabase para dados persistentes
  database: {
    provider: 'postgresql',
    url: process.env.SUPABASE_DATABASE_URL || 'postgresql://postgres.kpqxdptknmpsoswswrpd:<EMAIL>:6543/postgres',
  },

  // Session - JWT com Redis cache
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 horas
    updateAge: 60 * 60,   // Atualiza a cada 1 hora
  },

  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    algorithm: 'HS256',
  },

  // Email/Password provider
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // Pode habilitar depois
    minPasswordLength: 8,
    maxPasswordLength: 128,
  },

  // User schema
  user: {
    additionalFields: {
      role: {
        type: 'string',
        defaultValue: 'profissional',
        required: false,
        // Roles: superadmin, admin, profissional, inactive
      },
      name: {
        type: 'string',
        required: true,
      },
      phone: {
        type: 'string',
        required: false,
      },
      avatar: {
        type: 'string',
        required: false,
      },
    },
  },

  // Rate limiting
  rateLimit: {
    window: 60, // 1 minuto
    max: 10,    // 10 tentativas por minuto
    storage: 'redis',
    redis: authRedis,
  },

  // Security
  security: {
    csrf: true,
    cookieSecure: process.env.NODE_ENV === 'production',
    cookieSameSite: 'strict',
  },

  // Callbacks
  callbacks: {
    async signIn({ user, account }) {
      console.log(`🔐 User signed in: ${user.email}`);
      
      // Cache user data no Redis para performance
      const userCacheKey = `user:${user.id}`;
      await authRedis.setex(userCacheKey, 3600, JSON.stringify({
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        lastLogin: new Date().toISOString(),
      }));

      return true;
    },

    async signOut({ user }) {
      console.log(`🚪 User signed out: ${user.email}`);
      
      // Remove cache do usuário
      const userCacheKey = `user:${user.id}`;
      await authRedis.del(userCacheKey);
      
      return true;
    },

    async jwt({ token, user }) {
      // Adicionar dados customizados ao JWT
      if (user) {
        token.role = user.role;
        token.name = user.name;
      }
      return token;
    },
  },

  // Plugins
  plugins: [
    // Plugin para blacklist de tokens
    {
      id: 'token-blacklist',
      init: (auth) => {
        // Middleware para verificar tokens na blacklist
        auth.use('/api/auth/*', async (req, res, next) => {
          const token = req.headers.authorization?.replace('Bearer ', '');
          if (token) {
            const isBlacklisted = await authRedis.get(`blacklist:${token}`);
            if (isBlacklisted) {
              return res.status(401).json({ error: 'Token revoked' });
            }
          }
          next();
        });
      },
    },
  ],
});

// Utility functions para gerenciar tokens
export const authUtils = {
  // Adicionar token à blacklist
  async blacklistToken(token, expiresIn = 86400) {
    await authRedis.setex(`blacklist:${token}`, expiresIn, '1');
  },

  // Verificar se token está na blacklist
  async isTokenBlacklisted(token) {
    const result = await authRedis.get(`blacklist:${token}`);
    return !!result;
  },

  // Cache de usuário
  async getCachedUser(userId) {
    const cached = await authRedis.get(`user:${userId}`);
    return cached ? JSON.parse(cached) : null;
  },

  // Limpar cache de usuário
  async clearUserCache(userId) {
    await authRedis.del(`user:${userId}`);
  },

  // Rate limiting check
  async checkRateLimit(identifier, limit = 10, window = 60) {
    const key = `rate_limit:${identifier}`;
    const current = await authRedis.incr(key);
    
    if (current === 1) {
      await authRedis.expire(key, window);
    }
    
    return current <= limit;
  },
};

export default auth;
