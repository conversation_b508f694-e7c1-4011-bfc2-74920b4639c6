import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { profissionalService } from "@/services/database";

const TestPage = () => {
  const [testStatus, setTestStatus] = useState("Iniciando testes...");

  // Test database connection
  const { data: professionals, isLoading, error } = useQuery({
    queryKey: ['test-professionals'],
    queryFn: profissionalService.getAll,
    retry: 1
  });

  useEffect(() => {
    if (isLoading) {
      setTestStatus("🔄 Testando conexão com banco de dados...");
    } else if (error) {
      setTestStatus(`❌ Erro na conexão: ${error.message}`);
    } else {
      setTestStatus(`✅ Banco conectado! ${professionals?.length || 0} profissionais encontrados`);
    }
  }, [isLoading, error, professionals]);

  const testSupabaseEnv = () => {
    const url = import.meta.env.VITE_SUPABASE_URL;
    const key = import.meta.env.VITE_SUPABASE_PUBLISHABLE_KEY;

    console.log('Supabase URL:', url);
    console.log('Supabase Key:', key ? 'Configurada' : 'Não configurada');

    setTestStatus(`🔧 URL: ${url ? 'OK' : 'ERRO'} | Key: ${key ? 'OK' : 'ERRO'}`);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <CardHeader>
          <CardTitle>Página de Teste - Diagnóstico</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>Se você está vendo esta página, o React está funcionando corretamente!</p>

          <div>
            <h3 className="font-semibold">Status do Sistema:</h3>
            <ul className="mt-2 space-y-1">
              <li>✅ React carregado</li>
              <li>✅ Componentes UI funcionando</li>
              <li>✅ Roteamento ativo</li>
              <li>✅ TanStack Query funcionando</li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold">Teste de Banco de Dados:</h3>
            <p className="mt-2 p-2 bg-muted rounded">{testStatus}</p>
          </div>

          <div>
            <Button onClick={testSupabaseEnv} variant="outline">
              Testar Variáveis de Ambiente
            </Button>
          </div>

          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded">
              <h4 className="font-semibold text-red-800">Erro Detalhado:</h4>
              <pre className="mt-2 text-sm text-red-700">{JSON.stringify(error, null, 2)}</pre>
            </div>
          )}

          {professionals && (
            <div className="p-4 bg-green-50 border border-green-200 rounded">
              <h4 className="font-semibold text-green-800">Dados do Banco:</h4>
              <pre className="mt-2 text-sm text-green-700">{JSON.stringify(professionals, null, 2)}</pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TestPage;
