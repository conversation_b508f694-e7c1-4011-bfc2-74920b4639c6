// Using Supabase generated types
import { Database } from "@/integrations/supabase/types";

export type Cliente = Database['public']['Tables']['clientes']['Row'];
export type Profissional = Database['public']['Tables']['profissionais']['Row'];
export type HorarioTrabalho = Database['public']['Tables']['horarios_trabalho']['Row'];
export type Agendamento = Database['public']['Tables']['agendamentos']['Row'];
export type WebhookLog = Database['public']['Tables']['webhooks_log']['Row'];

export type CreateClienteData = Database['public']['Tables']['clientes']['Insert'];
export type CreateProfissionalData = Database['public']['Tables']['profissionais']['Insert'];
export type CreateHorarioTrabalhoData = Database['public']['Tables']['horarios_trabalho']['Insert'];
export type CreateAgendamentoData = Database['public']['Tables']['agendamentos']['Insert'];

// Additional types for extended data with relationships
export interface AgendamentoWithRelations extends Agendamento {
  cliente?: Pick<Cliente, 'nome' | 'numero_whatsapp'>;
  profissional?: Pick<Profissional, 'nome' | 'especialidade'>;
}