import { Alert<PERSON>riangle, <PERSON>, Clock, TrendingDown, ChevronRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ReminderData {
  weeklyNoShows: any[];
  clientsWithoutConfirmation: any[];
  inactiveClients: any[];
}

interface InteractiveRemindersProps {
  data: ReminderData;
  onViewDetails: (type: string, data: any[]) => void;
}

const InteractiveReminders = ({ data, onViewDetails }: InteractiveRemindersProps) => {
  const reminders = [
    {
      id: 'no_shows',
      title: 'Faltas na semana',
      description: `${data.weeklyNoShows.length} clientes faltaram esta semana`,
      count: data.weeklyNoShows.length,
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      threshold: 5,
      severity: data.weeklyNoShows.length > 5 ? 'high' : data.weeklyNoShows.length > 2 ? 'medium' : 'low',
      actionText: 'Ver clientes',
      data: data.weeklyNoShows
    },
    {
      id: 'unconfirmed',
      title: 'Sem confirmação',
      description: `${data.clientsWithoutConfirmation.length} clientes com atendimentos pendentes`,
      count: data.clientsWithoutConfirmation.length,
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      threshold: 3,
      severity: data.clientsWithoutConfirmation.length > 3 ? 'high' : data.clientsWithoutConfirmation.length > 1 ? 'medium' : 'low',
      actionText: 'Ver pendências',
      data: data.clientsWithoutConfirmation
    },
    {
      id: 'inactive',
      title: 'Clientes inativos',
      description: `${data.inactiveClients.length} clientes há mais de 30 dias sem retorno`,
      count: data.inactiveClients.length,
      icon: TrendingDown,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      threshold: 10,
      severity: data.inactiveClients.length > 10 ? 'high' : data.inactiveClients.length > 5 ? 'medium' : 'low',
      actionText: 'Ver inativos',
      data: data.inactiveClients
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-500';
      case 'medium':
        return 'bg-yellow-500';
      default:
        return 'bg-green-500';
    }
  };

  const visibleReminders = reminders.filter(reminder => 
    reminder.count > 0 || reminder.severity !== 'low'
  );

  if (visibleReminders.length === 0) {
    return (
      <Card className="shadow-card">
        <CardHeader>
          <CardTitle className="text-lg flex items-center space-x-2">
            <Users className="h-5 w-5 text-green-600" />
            <span>Lembretes</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <div className="text-green-600 mb-2">
              <Users className="h-8 w-8 mx-auto" />
            </div>
            <p className="text-sm text-muted-foreground">
              Tudo em ordem! Nenhum lembrete importante no momento.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-card">
      <CardHeader>
        <CardTitle className="text-lg flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            <span>Lembretes</span>
          </div>
          <Badge variant="outline" className="text-xs">
            {visibleReminders.length} ativo{visibleReminders.length !== 1 ? 's' : ''}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {visibleReminders.map((reminder) => {
            const Icon = reminder.icon;
            
            return (
              <div
                key={reminder.id}
                className={`p-3 rounded-lg border ${reminder.borderColor} ${reminder.bgColor} transition-all hover:shadow-sm`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="flex items-center space-x-2">
                      <Icon className={`h-4 w-4 ${reminder.color}`} />
                      <div className={`w-2 h-2 rounded-full ${getSeverityColor(reminder.severity)}`} />
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-sm">{reminder.title}</h4>
                        <Badge 
                          variant="secondary" 
                          className="text-xs px-1.5 py-0.5"
                        >
                          {reminder.count}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {reminder.description}
                      </p>
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2 text-xs"
                    onClick={() => onViewDetails(reminder.id, reminder.data)}
                  >
                    {reminder.actionText}
                    <ChevronRight className="h-3 w-3 ml-1" />
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
        
        {visibleReminders.length > 0 && (
          <div className="mt-4 pt-3 border-t">
            <p className="text-xs text-muted-foreground text-center">
              Clique em "Ver detalhes" para ações específicas
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default InteractiveReminders;
