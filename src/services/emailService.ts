import { supabase } from '@/integrations/supabase/client';

interface EmailNotificationData {
  to: string;
  clientName: string;
  professionalName: string;
  professionalEmail?: string;
  professionalPhone?: string;
  appointmentDate: string;
  appointmentTime: string;
  appointmentDuration?: number;
  appointmentNotes?: string;
  type: 'confirmation' | 'reminder' | 'cancellation' | 'no_show_alert';
}

export const emailService = {
  async sendNotification(data: EmailNotificationData): Promise<boolean> {
    try {
      console.log('Sending email notification:', data);

      const { data: result, error } = await supabase.functions.invoke('send-email-notification', {
        body: data
      });

      if (error) {
        console.error('Error sending email:', error);
        return false;
      }

      console.log('Email sent successfully:', result);
      return true;
    } catch (error) {
      console.error('Error in emailService.sendNotification:', error);
      return false;
    }
  },

  async sendConfirmationEmail(appointmentData: {
    clientEmail: string;
    clientName: string;
    professionalName: string;
    professionalEmail?: string;
    professionalPhone?: string;
    appointmentDate: string;
    appointmentTime: string;
    appointmentDuration?: number;
    appointmentNotes?: string;
  }): Promise<boolean> {
    return this.sendNotification({
      to: appointmentData.clientEmail,
      clientName: appointmentData.clientName,
      professionalName: appointmentData.professionalName,
      professionalEmail: appointmentData.professionalEmail,
      professionalPhone: appointmentData.professionalPhone,
      appointmentDate: appointmentData.appointmentDate,
      appointmentTime: appointmentData.appointmentTime,
      appointmentDuration: appointmentData.appointmentDuration,
      appointmentNotes: appointmentData.appointmentNotes,
      type: 'confirmation'
    });
  },

  async sendReminderEmail(appointmentData: {
    clientEmail: string;
    clientName: string;
    professionalName: string;
    appointmentDate: string;
    appointmentTime: string;
    appointmentDuration?: number;
  }): Promise<boolean> {
    return this.sendNotification({
      to: appointmentData.clientEmail,
      clientName: appointmentData.clientName,
      professionalName: appointmentData.professionalName,
      appointmentDate: appointmentData.appointmentDate,
      appointmentTime: appointmentData.appointmentTime,
      appointmentDuration: appointmentData.appointmentDuration,
      type: 'reminder'
    });
  },

  async sendCancellationEmail(appointmentData: {
    clientEmail: string;
    clientName: string;
    professionalName: string;
    appointmentDate: string;
    appointmentTime: string;
  }): Promise<boolean> {
    return this.sendNotification({
      to: appointmentData.clientEmail,
      clientName: appointmentData.clientName,
      professionalName: appointmentData.professionalName,
      appointmentDate: appointmentData.appointmentDate,
      appointmentTime: appointmentData.appointmentTime,
      type: 'cancellation'
    });
  },

  async sendNoShowAlert(appointmentData: {
    professionalEmail: string;
    clientName: string;
    professionalName: string;
    appointmentDate: string;
    appointmentTime: string;
  }): Promise<boolean> {
    return this.sendNotification({
      to: appointmentData.professionalEmail,
      clientName: appointmentData.clientName,
      professionalName: appointmentData.professionalName,
      appointmentDate: appointmentData.appointmentDate,
      appointmentTime: appointmentData.appointmentTime,
      type: 'no_show_alert'
    });
  },

  // Utility function to format date and time for emails
  formatAppointmentDateTime(dateTime: string): { date: string; time: string } {
    const appointmentDate = new Date(dateTime);
    
    const date = appointmentDate.toLocaleDateString('pt-BR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const time = appointmentDate.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });

    return { date, time };
  }
};

export default emailService;
