import { supabase } from '@/integrations/supabase/client';

export const automationService = {
  // Trigger auto presence check
  async triggerAutoPresenceCheck(): Promise<boolean> {
    try {
      console.log('Triggering auto presence check...');

      const { data, error } = await supabase.functions.invoke('auto-presence-check', {
        body: {}
      });

      if (error) {
        console.error('Error triggering auto presence check:', error);
        return false;
      }

      console.log('Auto presence check triggered successfully:', data);
      return true;
    } catch (error) {
      console.error('Error in automationService.triggerAutoPresenceCheck:', error);
      return false;
    }
  },

  // Trigger reminder emails
  async triggerReminderEmails(): Promise<boolean> {
    try {
      console.log('Triggering reminder emails...');

      const { data, error } = await supabase.functions.invoke('send-reminder-emails', {
        body: {}
      });

      if (error) {
        console.error('Error triggering reminder emails:', error);
        return false;
      }

      console.log('Reminder emails triggered successfully:', data);
      return true;
    } catch (error) {
      console.error('Error in automationService.triggerReminderEmails:', error);
      return false;
    }
  },

  // Schedule automation via N8N webhook
  async scheduleAutomation(type: 'presence_check' | 'reminder_emails', schedule?: string): Promise<boolean> {
    try {
      const payload = {
        event_type: 'schedule_automation',
        automation_type: type,
        schedule: schedule || (type === 'presence_check' ? '*/15 * * * *' : '0 18 * * *'), // Every 15 min for presence, 6 PM for reminders
        timestamp: new Date().toISOString(),
        data: {
          enabled: true,
          description: type === 'presence_check' 
            ? 'Auto mark no-show for appointments 15+ minutes late'
            : 'Send reminder emails for tomorrow appointments'
        }
      };

      const response = await fetch('https://automation.datanerd.com.br/webhook/schedule-automation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json().catch(() => ({}));
      console.log('Automation scheduled successfully:', result);
      return true;
    } catch (error) {
      console.error('Error scheduling automation:', error);
      return false;
    }
  },

  // Manual trigger for testing
  async manualTrigger(type: 'presence_check' | 'reminder_emails'): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      if (type === 'presence_check') {
        const success = await this.triggerAutoPresenceCheck();
        return { success };
      } else {
        const success = await this.triggerReminderEmails();
        return { success };
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
};

export default automationService;
