import { Agendamento, Cliente, Profissional } from './database';

interface WebhookPayload {
  event_type: 'agendamento_criado' | 'agendamento_atualizado' | 'agendamento_cancelado' | 'cliente_criado' | 'profissional_criado' | 'disponibilidade_alterada';
  timestamp: string;
  data: any;
}

const WEBHOOK_URLS = {
  agendamento: 'https://automation.datanerd.com.br/webhook/agendamento',
  agendamento_atualizado: 'https://automation.datanerd.com.br/webhook/agendamento-atualizado',
  agendamento_cancelado: 'https://automation.datanerd.com.br/webhook/cancelar',
  disponibilidade: 'https://automation.datanerd.com.br/webhook/disponibilidade',
};

export const sendWebhook = async (payload: WebhookPayload, webhookType: keyof typeof WEBHOOK_URLS) => {
  const url = WEBHOOK_URLS[webhookType];
  
  if (!url) {
    console.warn(`Webhook URL não configurada para tipo: ${webhookType}`);
    return null;
  }

  try {
    console.log(`Enviando webhook para ${url}:`, payload);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json().catch(() => ({}));
    
    console.log('Webhook enviado com sucesso:', result);
    return {
      success: true,
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('Erro ao enviar webhook:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido',
    };
  }
};

// Funções específicas para cada tipo de evento
export const notifyAgendamentoCriado = (agendamento: Agendamento, cliente: Cliente, profissional?: Profissional) => {
  return sendWebhook({
    event_type: 'agendamento_criado',
    timestamp: new Date().toISOString(),
    data: {
      agendamento,
      cliente,
      profissional,
    },
  }, 'agendamento');
};

export const notifyAgendamentoAtualizado = (agendamento: Agendamento, cliente: Cliente, profissional?: Profissional) => {
  return sendWebhook({
    event_type: 'agendamento_atualizado',
    timestamp: new Date().toISOString(),
    data: {
      agendamento,
      cliente,
      profissional,
    },
  }, 'agendamento_atualizado');
};

export const notifyAgendamentoCancelado = (agendamento: Agendamento, cliente: Cliente, profissional?: Profissional) => {
  return sendWebhook({
    event_type: 'agendamento_cancelado',
    timestamp: new Date().toISOString(),
    data: {
      agendamento,
      cliente,
      profissional,
    },
  }, 'agendamento_cancelado');
};

export const notifyDisponibilidadeAlterada = (
  profissionalId: string,
  diaSemana: number,
  ativo: boolean,
  bloqueioEventId?: string | null
) => {
  const payload = {
    event_type: 'disponibilidade_alterada' as const,
    timestamp: new Date().toISOString(),
    data: {
      profissional_id: profissionalId,
      dia_semana: diaSemana,
      acao: ativo ? 'desbloquear' : 'bloquear',
      intervalo: { inicio: '00:00', fim: '23:59' },
      bloqueio_event_id: bloqueioEventId || null,
    },
  };

  console.log('Enviando webhook de disponibilidade:', payload);
  return sendWebhook(payload, 'disponibilidade');
};