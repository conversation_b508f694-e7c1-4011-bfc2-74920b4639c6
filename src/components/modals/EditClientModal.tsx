import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { clienteService, type Cliente, type UpdateClienteData } from "@/services/database";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface EditClientModalProps {
  client: Cliente | null;
  isOpen: boolean;
  onClose: () => void;
}

const EditClientModal = ({ client, isOpen, onClose }: EditClientModalProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({
    nome: "",
    email: "",
    numero_whatsapp: ""
  });

  useEffect(() => {
    if (client) {
      setFormData({
        nome: client.nome || "",
        email: client.email || "",
        numero_whatsapp: client.numero_whatsapp || ""
      });
    }
  }, [client]);

  const updateClientMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateClienteData }) => 
      clienteService.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clients'] });
      toast({
        title: "Cliente atualizado!",
        description: "As informações do cliente foram atualizadas com sucesso.",
      });
      onClose();
    },
    onError: (error) => {
      console.error("Erro ao atualizar cliente:", error);
      toast({
        title: "Erro ao atualizar",
        description: "Ocorreu um erro ao atualizar o cliente. Tente novamente.",
        variant: "destructive"
      });
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!client) return;
    
    if (!formData.nome.trim()) {
      toast({
        title: "Nome obrigatório",
        description: "Por favor, informe o nome do cliente.",
        variant: "destructive"
      });
      return;
    }

    if (!formData.numero_whatsapp.trim()) {
      toast({
        title: "WhatsApp obrigatório",
        description: "Por favor, informe o número do WhatsApp.",
        variant: "destructive"
      });
      return;
    }

    updateClientMutation.mutate({
      id: client.id,
      data: {
        nome: formData.nome.trim(),
        email: formData.email.trim() || null,
        numero_whatsapp: formData.numero_whatsapp.trim()
      }
    });
  };

  const handleClose = () => {
    if (!updateClientMutation.isPending) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Editar Cliente</DialogTitle>
          <DialogDescription>
            Atualize as informações do cliente abaixo.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="nome">Nome *</Label>
              <Input
                id="nome"
                value={formData.nome}
                onChange={(e) => setFormData(prev => ({ ...prev, nome: e.target.value }))}
                placeholder="Nome completo do cliente"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">E-mail</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="whatsapp">WhatsApp *</Label>
              <Input
                id="whatsapp"
                value={formData.numero_whatsapp}
                onChange={(e) => setFormData(prev => ({ ...prev, numero_whatsapp: e.target.value }))}
                placeholder="(11) 99999-9999"
                required
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleClose}
              disabled={updateClientMutation.isPending}
            >
              Cancelar
            </Button>
            <Button 
              type="submit" 
              disabled={updateClientMutation.isPending}
            >
              {updateClientMutation.isPending ? "Salvando..." : "Salvar"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditClientModal;
