import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Layout from "./components/layout/Layout";
import PublicScheduling from "./pages/PublicScheduling";
import AdminDashboard from "./pages/AdminDashboard";
import AdminPatients from "./pages/AdminPatients";
import AdminSettings from "./pages/AdminSettings";
import AdminReports from "./pages/AdminReports";
import TestPage from "./pages/TestPage";
import TestRedisSystem from "./pages/TestRedisSystem";
import NotFound from "./pages/NotFound";

// Auth components
import { AuthProvider } from "@/hooks/useAuth";
import { ProtectedRoute, PublicOnlyRoute } from "@/components/auth/ProtectedRoute";
import { LoginForm } from "@/components/auth/LoginForm";
import { Dashboard } from "@/components/profissional/Dashboard";
import { SuperAdminDashboard } from "@/components/superadmin/SuperAdminDashboard";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <Routes>
            {/* Rota pública - Home (agendamento público) */}
            <Route path="/" element={<Layout><PublicScheduling /></Layout>} />

            {/* Rota de login - só acessível se NÃO estiver logado */}
            <Route
              path="/login"
              element={
                <PublicOnlyRoute>
                  <LoginForm />
                </PublicOnlyRoute>
              }
            />

            {/* Rotas protegidas - Área do Profissional */}
            <Route
              path="/profissional/dashboard"
              element={
                <ProtectedRoute requiredRole="profissional">
                  <Dashboard />
                </ProtectedRoute>
              }
            />

            {/* Rotas protegidas - SuperAdmin */}
            <Route
              path="/superadmin/dashboard"
              element={
                <ProtectedRoute requiredRole="superadmin">
                  <SuperAdminDashboard />
                </ProtectedRoute>
              }
            />

            {/* Rotas administrativas existentes (manter temporariamente) */}
            <Route path="/admin" element={<Layout><AdminDashboard /></Layout>} />
            <Route path="/admin/patients" element={<Layout><AdminPatients /></Layout>} />
            <Route path="/admin/reports" element={<Layout><AdminReports /></Layout>} />
            <Route path="/admin/settings" element={<Layout><AdminSettings /></Layout>} />
            <Route path="/test" element={<Layout><TestPage /></Layout>} />
            <Route path="/test-redis" element={<Layout><TestRedisSystem /></Layout>} />

            {/* Catch-all - redireciona para home */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
