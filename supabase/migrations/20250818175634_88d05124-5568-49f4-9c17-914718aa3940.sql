-- Create enum for status_agendamento if it doesn't exist
DO $$ BEGIN
    CREATE TYPE status_agendamento_enum AS ENUM ('pendente', 'confirmado', 'cancelado', 'concluído', 'ausente');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update clientes table to include missing columns
ALTER TABLE public.clientes 
ADD COLUMN IF NOT EXISTS cpf TEXT,
ADD COLUMN IF NOT EXISTS data_nascimento DATE,
ADD COLUMN IF NOT EXISTS endereco TEXT;

-- Update status_agendamento column to use the enum
ALTER TABLE public.clientes 
ALTER COLUMN status_agendamento TYPE status_agendamento_enum USING status_agendamento::status_agendamento_enum;

-- Create profissionais table
CREATE TABLE IF NOT EXISTS public.profissionais (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    nome TEXT NOT NULL,
    especialidade TEXT NOT NULL,
    crm TEXT,
    email TEXT NOT NULL,
    telefone TEXT,
    duracao_consulta INTEGER DEFAULT 30,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create horarios_trabalho table
CREATE TABLE IF NOT EXISTS public.horarios_trabalho (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    profissional_id UUID REFERENCES public.profissionais(id) ON DELETE CASCADE,
    dia_semana INTEGER NOT NULL CHECK (dia_semana >= 0 AND dia_semana <= 6),
    hora_inicio TIME NOT NULL,
    hora_fim TIME NOT NULL,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create agendamentos table
CREATE TABLE IF NOT EXISTS public.agendamentos (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    cliente_id UUID REFERENCES public.clientes(id) ON DELETE CASCADE,
    profissional_id UUID REFERENCES public.profissionais(id) ON DELETE CASCADE,
    data_hora TIMESTAMP WITH TIME ZONE NOT NULL,
    duracao INTEGER DEFAULT 30,
    status TEXT DEFAULT 'agendado',
    observacoes TEXT,
    google_event_id TEXT,
    webhook_enviado BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create webhooks_log table
CREATE TABLE IF NOT EXISTS public.webhooks_log (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    agendamento_id UUID REFERENCES public.agendamentos(id) ON DELETE CASCADE,
    tipo_evento TEXT NOT NULL,
    payload JSONB,
    status_resposta INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_horarios_trabalho_profissional_id ON public.horarios_trabalho(profissional_id);
CREATE INDEX IF NOT EXISTS idx_horarios_trabalho_dia_semana ON public.horarios_trabalho(dia_semana);
CREATE INDEX IF NOT EXISTS idx_agendamentos_cliente_id ON public.agendamentos(cliente_id);
CREATE INDEX IF NOT EXISTS idx_agendamentos_profissional_id ON public.agendamentos(profissional_id);
CREATE INDEX IF NOT EXISTS idx_agendamentos_data_hora ON public.agendamentos(data_hora);
CREATE INDEX IF NOT EXISTS idx_webhooks_log_agendamento_id ON public.webhooks_log(agendamento_id);