import { useState, useEffect, createContext, useContext, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';

// Tipos
interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  phone?: string;
  avatar?: string;
  lastLogin?: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
}

// Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// API Base URL
const API_BASE = process.env.NODE_ENV === 'production' 
  ? 'https://aptor.datanerd.com.br/api' 
  : 'http://localhost:3001/api';

// Provider
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  // Verificar se está autenticado
  const isAuthenticated = !!user && !!token;

  // Carregar token do localStorage na inicialização
  useEffect(() => {
    const savedToken = localStorage.getItem('aptor_token');
    const savedUser = localStorage.getItem('aptor_user');

    if (savedToken && savedUser) {
      try {
        setToken(savedToken);
        setUser(JSON.parse(savedUser));
        
        // Verificar se token ainda é válido
        validateToken(savedToken);
      } catch (error) {
        console.error('Error loading saved auth:', error);
        clearAuth();
      }
    }
    
    setIsLoading(false);
  }, []);

  // Validar token com o servidor
  const validateToken = async (tokenToValidate: string) => {
    try {
      const response = await fetch(`${API_BASE}/profissional/profile`, {
        headers: {
          'Authorization': `Bearer ${tokenToValidate}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Token inválido');
      }

      const data = await response.json();
      if (data.success) {
        setUser(data.data);
        localStorage.setItem('aptor_user', JSON.stringify(data.data));
      }
    } catch (error) {
      console.error('Token validation failed:', error);
      clearAuth();
    }
  };

  // Limpar autenticação
  const clearAuth = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('aptor_token');
    localStorage.removeItem('aptor_user');
  };

  // Login
  const login = async (email: string, password: string) => {
    try {
      const response = await fetch(`${API_BASE}/auth/sign-in`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Erro ao fazer login');
      }

      if (data.token && data.user) {
        setToken(data.token);
        setUser(data.user);

        // Salvar no localStorage
        localStorage.setItem('aptor_token', data.token);
        localStorage.setItem('aptor_user', JSON.stringify(data.user));

        // Redirecionar baseado na role
        const redirectPath = data.user.role === 'superadmin'
          ? '/superadmin/dashboard'
          : '/profissional/dashboard';

        navigate(redirectPath);
      } else {
        throw new Error('Resposta inválida do servidor');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      throw error;
    }
  };

  // Logout
  const logout = async () => {
    try {
      if (token) {
        // Chamar endpoint de logout no servidor
        await fetch(`${API_BASE}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      clearAuth();
      navigate('/login');
    }
  };

  // Refresh token
  const refreshToken = async () => {
    try {
      if (!token) return;

      const response = await fetch(`${API_BASE}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.token) {
          setToken(data.token);
          localStorage.setItem('aptor_token', data.token);
        }
      } else {
        // Token não pode ser renovado, fazer logout
        clearAuth();
        navigate('/login');
      }
    } catch (error) {
      console.error('Refresh token error:', error);
      clearAuth();
      navigate('/login');
    }
  };

  // Auto refresh token a cada 23 horas
  useEffect(() => {
    if (isAuthenticated) {
      const interval = setInterval(() => {
        refreshToken();
      }, 23 * 60 * 60 * 1000); // 23 horas

      return () => clearInterval(interval);
    }
  }, [isAuthenticated]);

  const value: AuthContextType = {
    user,
    token,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook para usar o contexto
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Hook para fazer requests autenticados
export function useAuthenticatedFetch() {
  const { token, logout } = useAuth();

  const authenticatedFetch = async (url: string, options: RequestInit = {}) => {
    if (!token) {
      throw new Error('Não autenticado');
    }

    const response = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    // Se token expirou, fazer logout
    if (response.status === 401) {
      logout();
      throw new Error('Sessão expirada');
    }

    return response;
  };

  return authenticatedFetch;
}
