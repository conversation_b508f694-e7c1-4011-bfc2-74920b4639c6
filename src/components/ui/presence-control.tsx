import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, Clock } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { clienteService, type Cliente } from "@/services/database";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface PresenceControlProps {
  client: Cliente;
  size?: "sm" | "md" | "lg";
  showLabel?: boolean;
}

const PresenceControl = ({ client, size = "md", showLabel = true }: PresenceControlProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isUpdating, setIsUpdating] = useState(false);

  const updatePresenceMutation = useMutation({
    mutationFn: ({ id, presenca_status }: { id: string; presenca_status: string }) => 
      clienteService.update(id, { presenca_status }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clients'] });
      toast({
        title: "Status atualizado!",
        description: "O status de presença foi atualizado com sucesso.",
      });
    },
    onError: (error) => {
      console.error("Erro ao atualizar presença:", error);
      toast({
        title: "Erro ao atualizar",
        description: "Ocorreu um erro ao atualizar o status. Tente novamente.",
        variant: "destructive"
      });
    },
    onSettled: () => {
      setIsUpdating(false);
    }
  });

  const handlePresenceChange = (newStatus: string) => {
    setIsUpdating(true);
    updatePresenceMutation.mutate({
      id: client.id,
      presenca_status: newStatus
    });
  };

  const getStatusDisplay = () => {
    const currentStatus = client.presenca_status || 'pending';
    
    switch (currentStatus) {
      case 'show':
        return {
          icon: CheckCircle,
          text: 'Veio',
          color: 'bg-green-100 text-green-800 border-green-200',
          buttonColor: 'bg-green-600 hover:bg-green-700'
        };
      case 'no_show':
        return {
          icon: XCircle,
          text: 'Faltou',
          color: 'bg-red-100 text-red-800 border-red-200',
          buttonColor: 'bg-red-600 hover:bg-red-700'
        };
      default:
        return {
          icon: Clock,
          text: 'Pendente',
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          buttonColor: 'bg-yellow-600 hover:bg-yellow-700'
        };
    }
  };

  const status = getStatusDisplay();
  const Icon = status.icon;
  
  const buttonSizes = {
    sm: "h-6 px-2 text-xs",
    md: "h-8 px-3 text-sm",
    lg: "h-10 px-4 text-base"
  };

  const iconSizes = {
    sm: "h-3 w-3",
    md: "h-4 w-4", 
    lg: "h-5 w-5"
  };

  if (showLabel) {
    return (
      <div className="flex items-center space-x-2">
        <Badge className={status.color} variant="outline">
          <Icon className={`${iconSizes[size]} mr-1`} />
          {status.text}
        </Badge>
        
        <div className="flex space-x-1">
          {client.presenca_status !== 'show' && (
            <Button
              size="sm"
              variant="outline"
              className={`${buttonSizes[size]} text-green-700 border-green-300 hover:bg-green-50`}
              onClick={() => handlePresenceChange('show')}
              disabled={isUpdating}
              title="Marcar como presente"
            >
              <CheckCircle className={`${iconSizes[size]} ${size === 'sm' ? '' : 'mr-1'}`} />
              {size !== 'sm' && 'Veio'}
            </Button>
          )}
          
          {client.presenca_status !== 'no_show' && (
            <Button
              size="sm"
              variant="outline"
              className={`${buttonSizes[size]} text-red-700 border-red-300 hover:bg-red-50`}
              onClick={() => handlePresenceChange('no_show')}
              disabled={isUpdating}
              title="Marcar como ausente"
            >
              <XCircle className={`${iconSizes[size]} ${size === 'sm' ? '' : 'mr-1'}`} />
              {size !== 'sm' && 'Faltou'}
            </Button>
          )}
          
          {client.presenca_status !== 'pending' && (
            <Button
              size="sm"
              variant="outline"
              className={`${buttonSizes[size]} text-yellow-700 border-yellow-300 hover:bg-yellow-50`}
              onClick={() => handlePresenceChange('pending')}
              disabled={isUpdating}
              title="Marcar como pendente"
            >
              <Clock className={`${iconSizes[size]} ${size === 'sm' ? '' : 'mr-1'}`} />
              {size !== 'sm' && 'Pendente'}
            </Button>
          )}
        </div>
      </div>
    );
  }

  // Compact version - just the status badge
  return (
    <Badge className={status.color} variant="outline">
      <Icon className={`${iconSizes[size]} mr-1`} />
      {status.text}
    </Badge>
  );
};

export default PresenceControl;
