import { useState, useEffect } from "react";
import { Calendar, Clock, User, Phone, Mail, FileText, Heart, Instagram, Facebook, Linkedin, Globe, MessageCircle, LogIn } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation } from "@tanstack/react-query";
import { profissionalService, agendamentoService, clienteService } from "@/services/database";
import emailService from "@/services/emailService";
import availabilityService from "@/services/availabilityService";

const PublicScheduling = () => {
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [selectedTime, setSelectedTime] = useState<string>("");
  const [selectedProfessional, setSelectedProfessional] = useState<string>("");
  const [sessionId] = useState<string>(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const [patientData, setPatientData] = useState({
    name: "",
    email: "",
    phone: "",
    notes: ""
  });
  const { toast } = useToast();

  // Fetch professionals
  const { data: professionals = [], isLoading } = useQuery({
    queryKey: ['professionals'],
    queryFn: profissionalService.getAll
  });

  // Get first professional by default
  useEffect(() => {
    if (professionals.length > 0 && !selectedProfessional) {
      setSelectedProfessional(professionals[0].id);
    }
  }, [professionals, selectedProfessional]);

  // Mock available dates (next 5 days)
  const getAvailableDates = () => {
    const dates = [];
    const today = new Date();
    
    for (let i = 1; i <= 5; i++) {
      const date = new Date();
      date.setDate(today.getDate() + i);
      dates.push({
        date: date.toISOString().split('T')[0],
        displayDate: date.toLocaleDateString('pt-BR', { 
          weekday: 'long', 
          day: 'numeric', 
          month: 'long' 
        })
      });
    }
    return dates;
  };

  // Fetch available time slots with Redis cache
  const { data: availableSlots = [], isLoading: slotsLoading, refetch: refetchSlots } = useQuery({
    queryKey: ['availableSlots', selectedProfessional, selectedDate],
    queryFn: () => availabilityService.getAvailableSlots(selectedProfessional, selectedDate),
    enabled: !!(selectedProfessional && selectedDate),
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // Refetch every minute
  });

  // Extract available times from slots
  const availableTimes = availableSlots
    .filter(slot => slot.available && !slot.locked)
    .map(slot => slot.time);

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Trim whitespace from required fields
    const trimmedName = patientData.name.trim();
    const trimmedPhone = patientData.phone.trim();

    if (!selectedDate || !selectedTime || !trimmedName || !trimmedPhone) {
      toast({
        title: "Campos obrigatórios",
        description: "Por favor, preencha todos os campos obrigatórios.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // 🔒 STEP 1: Try to lock the time slot
      console.log(`🔒 Attempting to lock slot: ${selectedProfessional} at ${selectedDate}T${selectedTime}`);
      const locked = await availabilityService.lockTimeSlot(
        selectedProfessional,
        selectedDate,
        selectedTime,
        sessionId
      );

      if (!locked) {
        toast({
          title: "Horário indisponível",
          description: "Este horário acabou de ser reservado por outro usuário. Por favor, escolha outro horário.",
          variant: "destructive",
        });
        // Refresh available slots
        refetchSlots();
        return;
      }

      console.log(`✅ Slot locked successfully for session ${sessionId}`);

      // 🔒 STEP 2: Proceed with booking (we have the lock)
      // Create or find client
      let cliente = await clienteService.searchByWhatsApp(trimmedPhone);
      if (!cliente) {
        cliente = await clienteService.create({
          nome: trimmedName,
          numero_whatsapp: trimmedPhone,
          email: patientData.email?.trim() || null
        });
      }

      // Get professional data
      const profissional = await profissionalService.getById(selectedProfessional);
      if (!profissional) {
        throw new Error("Profissional não encontrado");
      }

      // Create appointment with proper timezone
      const [year, month, day] = selectedDate.split('-');
      const [hour, minute] = selectedTime.split(':');
      const dataHora = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), parseInt(hour), parseInt(minute));
      
      const novoAgendamento = await agendamentoService.create({
        cliente_id: cliente.id,
        profissional_id: selectedProfessional,
        data_hora: dataHora.toISOString(),
        observacoes: patientData.notes?.trim() || null
      });

      console.log(`✅ Appointment created successfully: ${novoAgendamento.id}`);

      // 🔓 STEP 3: Unlock the time slot (we're done with it)
      await availabilityService.unlockTimeSlot(
        selectedProfessional,
        selectedDate,
        selectedTime,
        sessionId
      );

      // 🗑️ STEP 4: Invalidate cache to reflect the new booking
      await availabilityService.invalidateCache(selectedProfessional, selectedDate);

      // Send webhook to N8N
      try {
        const { notifyAgendamentoCriado } = await import('../services/webhooks');
        await notifyAgendamentoCriado(novoAgendamento, cliente, profissional);
      } catch (webhookError) {
        console.warn("Erro ao enviar webhook:", webhookError);
        // Don't fail the entire process if webhook fails
      }

      // Send confirmation email if client has email
      if (cliente.email) {
        try {
          const { date, time } = emailService.formatAppointmentDateTime(novoAgendamento.data_hora);

          await emailService.sendConfirmationEmail({
            clientEmail: cliente.email,
            clientName: cliente.nome,
            professionalName: profissional.nome,
            professionalEmail: profissional.email,
            professionalPhone: profissional.telefone,
            appointmentDate: date,
            appointmentTime: time,
            appointmentDuration: profissional.duracao_consulta || 30,
            appointmentNotes: novoAgendamento.observacoes
          });

          console.log('Confirmation email sent successfully');
        } catch (emailError) {
          console.error('Error sending confirmation email:', emailError);
          // Don't fail the appointment creation if email fails
        }
      }

      toast({
        title: "Agendamento realizado!",
        description: cliente.email
          ? "Seu agendamento foi confirmado e um email de confirmação foi enviado."
          : "Seu agendamento foi confirmado. Você receberá uma confirmação em breve.",
      });

      // Reset form
      setSelectedDate("");
      setSelectedTime("");
      setPatientData({ name: "", email: "", phone: "", notes: "" });

      // Refresh available slots to show updated availability
      refetchSlots();

    } catch (error) {
      console.error("Erro ao criar agendamento:", error);

      // 🔓 STEP 5: Unlock the time slot on error
      try {
        await availabilityService.unlockTimeSlot(
          selectedProfessional,
          selectedDate,
          selectedTime,
          sessionId
        );
        console.log(`🔓 Unlocked slot after error for session ${sessionId}`);
      } catch (unlockError) {
        console.error("Error unlocking slot after failure:", unlockError);
      }

      toast({
        title: "Erro ao agendar",
        description: "Ocorreu um erro ao realizar o agendamento. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const availableDates = getAvailableDates();

  return (
    <div className="min-h-screen bg-gradient-secondary">
      <div className="container mx-auto px-4 py-8">
        {/* Header com botão de login */}
        <div className="flex justify-end mb-6">
          <Link to="/login">
            <Button variant="outline" className="flex items-center space-x-2">
              <LogIn className="w-4 h-4" />
              <span>Área do Profissional</span>
            </Button>
          </Link>
        </div>

        {/* Hero Section */}
        <div className="text-center mb-12 animate-fade-in-up">
          <h1 className="text-4xl md:text-5xl font-bold text-primary mb-4">
            Agende seu Atendimento
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Escolha o melhor horário para seu atendimento de forma rápida e prática
          </p>
        </div>

        <div className="max-w-4xl mx-auto grid gap-8 md:grid-cols-2">
          {/* Professional Info Card */}
          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
            <CardHeader className="text-center">
              <div className="w-20 h-20 bg-gradient-primary rounded-full mx-auto mb-4 flex items-center justify-center">
                <Heart className="h-10 w-10 text-white" />
              </div>
              <CardTitle className="text-2xl">
                {isLoading ? "Carregando..." : professionals.length > 0 ? professionals[0].nome : "Nenhum profissional disponível"}
              </CardTitle>
              <CardDescription className="text-lg">
                {professionals.length > 0 ? (
                  <>
                    {professionals[0].especialidade}
                    {(professionals[0].conselho_sigla && professionals[0].registro_conselho) ? 
                      ` - ${professionals[0].conselho_sigla} ${professionals[0].registro_conselho}` : 
                      professionals[0].crm ? ` - CRM ${professionals[0].crm}` : ""
                    }
                  </>
                ) : "Configure um profissional na área administrativa"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3 text-muted-foreground">
                <Clock className="h-5 w-5 text-primary" />
                <span>Atendimentos de {professionals.length > 0 ? professionals[0].duracao_consulta : 30} minutos</span>
              </div>
              <div className="flex items-center space-x-3 text-muted-foreground">
                <Calendar className="h-5 w-5 text-primary" />
                <span>Agendamento até 5 dias</span>
              </div>
              {professionals.length > 0 && professionals[0].informacoes_importantes && (
                <div className="mt-6 p-4 bg-secondary rounded-lg">
                  <h4 className="font-semibold text-primary mb-2">Informações importantes:</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    {professionals[0].informacoes_importantes.split('\n').map((info, index) => (
                      <div key={index}>• {info}</div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Contact Information */}
              <div className="mt-4 p-4 bg-card rounded-lg border">
                <h4 className="font-semibold text-primary mb-3">Contato:</h4>
                <div className="space-y-2 text-sm">
                  {professionals[0]?.email && (
                    <div className="flex items-center space-x-2">
                      <Mail className="h-4 w-4 text-primary" />
                      <span>{professionals[0].email}</span>
                    </div>
                  )}
                  {professionals[0]?.telefone && (
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4 text-primary" />
                      <span>{professionals[0].telefone}</span>
                    </div>
                  )}
                </div>

                {/* Social Media Links */}
                {professionals.length > 0 && professionals[0].redes_sociais && Object.keys(professionals[0].redes_sociais).some(key => professionals[0].redes_sociais[key]) && (
                  <div className="mt-4 pt-3 border-t">
                    <h5 className="font-medium text-primary mb-2">Redes Sociais:</h5>
                    <div className="flex flex-wrap gap-2">
                      {professionals[0].redes_sociais.instagram && (
                        <a
                          href={professionals[0].redes_sociais.instagram}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-1 px-2 py-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-md text-xs hover:opacity-90 transition-opacity"
                        >
                          <Instagram className="h-3 w-3" />
                          <span>Instagram</span>
                        </a>
                      )}
                      {professionals[0].redes_sociais.facebook && (
                        <a
                          href={professionals[0].redes_sociais.facebook}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-1 px-2 py-1 bg-blue-600 text-white rounded-md text-xs hover:opacity-90 transition-opacity"
                        >
                          <Facebook className="h-3 w-3" />
                          <span>Facebook</span>
                        </a>
                      )}
                      {professionals[0].redes_sociais.linkedin && (
                        <a
                          href={professionals[0].redes_sociais.linkedin}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-1 px-2 py-1 bg-blue-700 text-white rounded-md text-xs hover:opacity-90 transition-opacity"
                        >
                          <Linkedin className="h-3 w-3" />
                          <span>LinkedIn</span>
                        </a>
                      )}
                      {professionals[0].redes_sociais.website && (
                        <a
                          href={professionals[0].redes_sociais.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-1 px-2 py-1 bg-gray-600 text-white rounded-md text-xs hover:opacity-90 transition-opacity"
                        >
                          <Globe className="h-3 w-3" />
                          <span>Website</span>
                        </a>
                      )}
                      {professionals[0].redes_sociais.whatsapp && (
                        <a
                          href={professionals[0].redes_sociais.whatsapp}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-1 px-2 py-1 bg-green-600 text-white rounded-md text-xs hover:opacity-90 transition-opacity"
                        >
                          <MessageCircle className="h-3 w-3" />
                          <span>WhatsApp</span>
                        </a>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Scheduling Form */}
          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-5 w-5 text-primary" />
                <span>Escolha Data e Horário</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Date Selection */}
                <div className="space-y-3">
                  <Label className="text-base font-medium">Data do Atendimento</Label>
                  <div className="grid gap-2">
                    {availableDates.map((dateInfo) => (
                      <Button
                        key={dateInfo.date}
                        type="button"
                        variant={selectedDate === dateInfo.date ? "default" : "outline"}
                        className="justify-start h-auto p-3"
                        onClick={() => setSelectedDate(dateInfo.date)}
                      >
                        <div className="text-left">
                        <div className="font-medium">{dateInfo.displayDate}</div>
                        <div className="text-sm opacity-75">Horários disponíveis</div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Time Selection */}
                {selectedDate && (
                  <div className="space-y-3">
                    <Label className="text-base font-medium">Horário</Label>
                    <div className="grid grid-cols-4 gap-2">
                      {availableTimes.map((time) => (
                        <Button
                          key={time}
                          type="button"
                          variant={selectedTime === time ? "default" : "outline"}
                          className="h-10"
                          onClick={() => setSelectedTime(time)}
                        >
                          {time}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Patient Data */}
                {selectedTime && (
                  <div className="space-y-4 pt-4 border-t">
                    <div className="flex items-center space-x-2 mb-4">
                      <FileText className="h-5 w-5 text-primary" />
                      <Label className="text-base font-medium">Seus Dados</Label>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <Label htmlFor="name">Nome Completo *</Label>
                        <Input
                          id="name"
                          value={patientData.name}
                          onChange={(e) => setPatientData(prev => ({ ...prev, name: e.target.value }))}
                          placeholder="Seu nome completo"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="phone">Telefone *</Label>
                        <Input
                          id="phone"
                          value={patientData.phone}
                          onChange={(e) => setPatientData(prev => ({ ...prev, phone: e.target.value }))}
                          placeholder="(11) 99999-9999"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="email">E-mail</Label>
                        <Input
                          id="email"
                          type="email"
                          value={patientData.email}
                          onChange={(e) => setPatientData(prev => ({ ...prev, email: e.target.value }))}
                          placeholder="<EMAIL>"
                        />
                      </div>

                      <div>
                        <Label htmlFor="notes">Observações</Label>
                        <Textarea
                          id="notes"
                          value={patientData.notes}
                          onChange={(e) => setPatientData(prev => ({ ...prev, notes: e.target.value }))}
                          placeholder="Motivo do atendimento ou observações importantes"
                          rows={3}
                        />
                      </div>
                    </div>

                    {/* Summary */}
                    <div className="bg-primary/10 p-4 rounded-lg">
                      <h4 className="font-semibold text-primary mb-2">Resumo do Agendamento</h4>
                      <div className="space-y-1 text-sm">
                        <div>Data: <Badge variant="secondary">{availableDates.find(d => d.date === selectedDate)?.displayDate}</Badge></div>
                        <div>Horário: <Badge variant="secondary">{selectedTime}</Badge></div>
                        <div>Profissional: <Badge variant="secondary">{professionals.length > 0 ? professionals[0].nome : ""}</Badge></div>
                      </div>
                    </div>

                    <Button 
                      type="submit" 
                      className="w-full bg-gradient-primary shadow-elegant hover:shadow-glow"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Agendando..." : "Confirmar Agendamento"}
                    </Button>
                  </div>
                )}
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PublicScheduling;