-- Enable Row Level Security on all public tables
ALTER TABLE public.agendamentos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clientes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.horarios_trabalho ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profissionais ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.webhooks_log ENABLE ROW LEVEL SECURITY;

-- Create basic RLS policies for public access (for now, since no auth is implemented)
-- These will need to be updated when authentication is implemented

-- Allow all operations on profissionais (public scheduling needs to read)
CREATE POLICY "Allow all operations on profissionais" ON public.profissionais
FOR ALL USING (true) WITH CHECK (true);

-- Allow all operations on clientes  
CREATE POLICY "Allow all operations on clientes" ON public.clientes
FOR ALL USING (true) WITH CHECK (true);

-- Allow all operations on agendamentos
CREATE POLICY "Allow all operations on agendamentos" ON public.agendamentos
FOR ALL USING (true) WITH CHECK (true);

-- Allow all operations on horarios_trabalho
CREATE POLICY "Allow all operations on horarios_trabalho" ON public.horarios_trabalho
FOR ALL USING (true) WITH CHECK (true);

-- Allow all operations on webhooks_log
CREATE POLICY "Allow all operations on webhooks_log" ON public.webhooks_log
FOR ALL USING (true) WITH CHECK (true);