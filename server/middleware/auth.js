import jwt from 'jsonwebtoken';
import { authUtils } from '../auth.js';

// Middleware para verificar autenticação
export const requireAuth = async (req, res, next) => {
  try {
    // Extrair token do header Authorization
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized',
        message: 'Token de acesso requerido'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer '

    // Verificar se token está na blacklist
    const isBlacklisted = await authUtils.isTokenBlacklisted(token);
    if (isBlacklisted) {
      return res.status(401).json({
        success: false,
        error: 'Token revoked',
        message: 'Token foi revogado'
      });
    }

    // Verificar e decodificar JWT
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production');
    
    // Buscar dados do usuário (cache primeiro, depois DB)
    let user = await authUtils.getCachedUser(decoded.sub);
    
    if (!user) {
      // Se não está em cache, buscar do Supabase e cachear
      // Por enquanto, usar dados do JWT
      user = {
        id: decoded.sub,
        email: decoded.email,
        name: decoded.name,
        role: decoded.role || 'profissional',
      };
      
      // Cachear por 1 hora
      await authUtils.getCachedUser(user.id); // Isso vai cachear automaticamente
    }

    // Adicionar usuário ao request
    req.user = user;
    req.token = token;

    next();
  } catch (error) {
    console.error('❌ Auth middleware error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: 'Invalid token',
        message: 'Token inválido'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'Token expired',
        message: 'Token expirado'
      });
    }

    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Erro interno do servidor'
    });
  }
};

// Middleware para verificar role específica
export const requireRole = (requiredRole) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized',
        message: 'Usuário não autenticado'
      });
    }

    if (req.user.role !== requiredRole) {
      return res.status(403).json({
        success: false,
        error: 'Forbidden',
        message: `Acesso negado. Role '${requiredRole}' requerida.`
      });
    }

    next();
  };
};

// Middleware para verificar se é profissional
export const requireProfissional = requireRole('profissional');

// Middleware para verificar se é superadmin
export const requireSuperAdmin = requireRole('superadmin');

// Middleware para verificar se é admin ou superadmin
export const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Unauthorized',
      message: 'Usuário não autenticado'
    });
  }

  if (!['admin', 'superadmin'].includes(req.user.role)) {
    return res.status(403).json({
      success: false,
      error: 'Forbidden',
      message: 'Acesso negado. Permissões de administrador requeridas.'
    });
  }

  next();
};

// Middleware para verificar se usuário está ativo
export const requireActiveUser = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Unauthorized',
      message: 'Usuário não autenticado'
    });
  }

  if (req.user.role === 'inactive') {
    return res.status(403).json({
      success: false,
      error: 'Account suspended',
      message: 'Sua conta foi suspensa. Entre em contato com o suporte.'
    });
  }

  next();
};

// Middleware opcional de auth (não falha se não autenticado)
export const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      // Verificar se não está na blacklist
      const isBlacklisted = await authUtils.isTokenBlacklisted(token);
      if (!isBlacklisted) {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production');
        
        // Buscar dados do usuário
        const user = await authUtils.getCachedUser(decoded.sub) || {
          id: decoded.sub,
          email: decoded.email,
          name: decoded.name,
          role: decoded.role || 'profissional',
        };
        
        req.user = user;
        req.token = token;
      }
    }
  } catch (error) {
    // Ignorar erros em auth opcional
    console.log('Optional auth failed:', error.message);
  }
  
  next();
};

// Rate limiting middleware
export const rateLimit = (identifier = 'ip', limit = 10, window = 60) => {
  return async (req, res, next) => {
    try {
      let rateLimitKey;
      
      switch (identifier) {
        case 'ip':
          rateLimitKey = req.ip || req.connection.remoteAddress;
          break;
        case 'user':
          rateLimitKey = req.user?.id || req.ip;
          break;
        case 'email':
          rateLimitKey = req.body?.email || req.ip;
          break;
        default:
          rateLimitKey = req.ip;
      }

      const allowed = await authUtils.checkRateLimit(rateLimitKey, limit, window);
      
      if (!allowed) {
        return res.status(429).json({
          success: false,
          error: 'Rate limit exceeded',
          message: `Muitas tentativas. Tente novamente em ${window} segundos.`
        });
      }

      next();
    } catch (error) {
      console.error('Rate limit error:', error);
      next(); // Continuar em caso de erro no rate limiting
    }
  };
};
