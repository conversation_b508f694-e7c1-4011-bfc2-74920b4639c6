# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Frontend build files (keep source for building)
dist/
build/
# src/ - NEEDED FOR BUILD
# public/ - NEEDED FOR BUILD
# index.html - NEEDED FOR BUILD
# vite.config.ts - NEEDED FOR BUILD
# tsconfig*.json - NEEDED FOR BUILD

# Development files
.git/
.gitignore
README.md
*.md
.vscode/
.idea/

# Environment files (will be copied explicitly)
# .env*

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file (handled explicitly)
# .env

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Docker
Dockerfile*
docker-compose*
.dockerignore
