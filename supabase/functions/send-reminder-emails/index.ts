import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('Starting reminder email check...');

    // Get current time and tomorrow's date range
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const tomorrowStart = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 0, 0, 0);
    const tomorrowEnd = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 23, 59, 59);

    console.log(`Checking appointments for tomorrow: ${tomorrowStart.toISOString()} to ${tomorrowEnd.toISOString()}`);

    // Find appointments for tomorrow that need reminders
    const { data: appointmentsToRemind, error: appointmentsError } = await supabaseClient
      .from('agendamentos')
      .select(`
        id,
        data_hora,
        status,
        observacoes,
        clientes (
          id,
          nome,
          email
        ),
        profissionais (
          id,
          nome,
          duracao_consulta
        )
      `)
      .gte('data_hora', tomorrowStart.toISOString())
      .lte('data_hora', tomorrowEnd.toISOString())
      .in('status', ['agendado', 'confirmado'])
      .not('clientes.email', 'is', null);

    if (appointmentsError) {
      console.error('Error fetching appointments:', appointmentsError);
      throw appointmentsError;
    }

    console.log(`Found ${appointmentsToRemind?.length || 0} appointments to send reminders`);

    let emailsSent = 0;
    let emailsFailed = 0;

    if (appointmentsToRemind && appointmentsToRemind.length > 0) {
      for (const appointment of appointmentsToRemind) {
        try {
          if (!appointment.clientes?.email) {
            console.log(`Skipping appointment ${appointment.id} - no client email`);
            continue;
          }

          const appointmentDate = new Date(appointment.data_hora);
          const formattedDate = appointmentDate.toLocaleDateString('pt-BR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
          const formattedTime = appointmentDate.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
          });

          // Send reminder email
          const { error: emailError } = await supabaseClient.functions.invoke('send-email-notification', {
            body: {
              to: appointment.clientes.email,
              clientName: appointment.clientes.nome,
              professionalName: appointment.profissionais?.nome || 'Profissional',
              appointmentDate: formattedDate,
              appointmentTime: formattedTime,
              appointmentDuration: appointment.profissionais?.duracao_consulta || 30,
              appointmentNotes: appointment.observacoes,
              type: 'reminder'
            }
          });

          if (emailError) {
            console.error(`Error sending reminder email for appointment ${appointment.id}:`, emailError);
            emailsFailed++;
          } else {
            emailsSent++;
            console.log(`Reminder email sent for appointment ${appointment.id} to ${appointment.clientes.email}`);
          }

        } catch (error) {
          console.error(`Error processing reminder for appointment ${appointment.id}:`, error);
          emailsFailed++;
        }
      }
    }

    // Log results
    console.log(`Reminder email check completed. Sent ${emailsSent} emails, ${emailsFailed} failed.`);

    return new Response(
      JSON.stringify({
        success: true,
        message: `Reminder email check completed`,
        appointmentsChecked: appointmentsToRemind?.length || 0,
        emailsSent: emailsSent,
        emailsFailed: emailsFailed,
        timestamp: now.toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Error in reminder email check:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
})
