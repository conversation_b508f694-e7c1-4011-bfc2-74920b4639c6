import { TrendingUp, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { agendamentoService, clienteService, profissionalService } from "@/services/database";

const ProfessionalAnalytics = () => {
  // Fetch data for analytics
  const { data: appointments = [] } = useQuery({
    queryKey: ['appointments'],
    queryFn: agendamentoService.getAll
  });

  const { data: clients = [] } = useQuery({
    queryKey: ['clients'],
    queryFn: clienteService.getAll
  });

  const { data: professionals = [] } = useQuery({
    queryKey: ['professionals'],
    queryFn: profissionalService.getAll
  });

  // Calculate analytics for each professional
  const professionalStats = professionals.map(prof => {
    const profAppointments = appointments.filter(apt => apt.profissional_id === prof.id);
    const profClients = [...new Set(profAppointments.map(apt => apt.cliente_id))];
    
    // Calculate performance metrics
    const totalAppointments = profAppointments.length;
    const completedAppointments = profAppointments.filter(apt => apt.status === 'confirmado').length;
    const cancelledAppointments = profAppointments.filter(apt => apt.status === 'cancelado').length;
    const noShowAppointments = profAppointments.filter(apt => apt.status === 'no_show').length;
    
    const completionRate = totalAppointments > 0 ? (completedAppointments / totalAppointments) * 100 : 0;
    const cancellationRate = totalAppointments > 0 ? (cancelledAppointments / totalAppointments) * 100 : 0;
    const noShowRate = totalAppointments > 0 ? (noShowAppointments / totalAppointments) * 100 : 0;

    // Calculate this month's data
    const thisMonth = new Date();
    thisMonth.setDate(1);
    const thisMonthAppointments = profAppointments.filter(apt => 
      new Date(apt.data_hora) >= thisMonth
    ).length;

    return {
      id: prof.id,
      name: prof.nome,
      specialty: prof.especialidade,
      totalAppointments,
      totalClients: profClients.length,
      completionRate: Math.round(completionRate),
      cancellationRate: Math.round(cancellationRate),
      noShowRate: Math.round(noShowRate),
      thisMonthAppointments,
      avgDuration: prof.duracao_consulta || 30
    };
  });

  // Overall analytics
  const totalAppointments = appointments.length;
  const totalClients = clients.length;
  const avgCompletionRate = professionalStats.length > 0 
    ? Math.round(professionalStats.reduce((sum, prof) => sum + prof.completionRate, 0) / professionalStats.length)
    : 0;

  const getPerformanceColor = (rate: number) => {
    if (rate >= 90) return "bg-success text-success-foreground";
    if (rate >= 70) return "bg-warning text-warning-foreground";
    return "bg-destructive text-destructive-foreground";
  };

  return (
    <div className="space-y-8">
      {/* Overall Analytics */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Atendimentos</CardTitle>
            <Calendar className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">{totalAppointments}</div>
            <p className="text-xs text-muted-foreground">
              Todos os profissionais
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Clientes</CardTitle>
            <Users className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">{totalClients}</div>
            <p className="text-xs text-muted-foreground">
              Clientes únicos
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Conclusão Média</CardTitle>
            <TrendingUp className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">{avgCompletionRate}%</div>
            <p className="text-xs text-muted-foreground">
              Média entre profissionais
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Professional Performance */}
      <Card className="shadow-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-primary" />
            <span>Performance por Profissional</span>
          </CardTitle>
          <CardDescription>
            Métricas detalhadas de desempenho e estatísticas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {professionalStats.map((prof, index) => (
              <div 
                key={prof.id}
                className="p-6 bg-gradient-card rounded-lg border animate-fade-in-up"
                style={{ animationDelay: `${0.1 + index * 0.1}s` }}
              >
                {/* Professional Header */}
                <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-primary">{prof.name}</h3>
                    <p className="text-sm text-muted-foreground">{prof.specialty}</p>
                  </div>
                  <Badge className={getPerformanceColor(prof.completionRate)}>
                    {prof.completionRate}% de conclusão
                  </Badge>
                </div>

                {/* Metrics Grid */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <div className="text-center p-3 bg-background rounded-lg">
                    <div className="text-2xl font-bold text-primary">{prof.totalAppointments}</div>
                    <div className="text-xs text-muted-foreground">Total de Atendimentos</div>
                  </div>
                  
                  <div className="text-center p-3 bg-background rounded-lg">
                    <div className="text-2xl font-bold text-primary">{prof.totalClients}</div>
                    <div className="text-xs text-muted-foreground">Clientes Únicos</div>
                  </div>
                  
                  <div className="text-center p-3 bg-background rounded-lg">
                    <div className="text-2xl font-bold text-primary">{prof.thisMonthAppointments}</div>
                    <div className="text-xs text-muted-foreground">Este Mês</div>
                  </div>
                  
                  <div className="text-center p-3 bg-background rounded-lg">
                    <div className="text-2xl font-bold text-primary">{prof.avgDuration}min</div>
                    <div className="text-xs text-muted-foreground">Duração Média</div>
                  </div>
                </div>

                {/* Performance Indicators */}
                <div className="grid gap-3 md:grid-cols-3 mt-4">
                  <div className="flex items-center justify-between p-3 bg-background rounded-lg">
                    <span className="text-sm text-muted-foreground">Conclusão</span>
                    <Badge className={getPerformanceColor(prof.completionRate)}>
                      {prof.completionRate}%
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-background rounded-lg">
                    <span className="text-sm text-muted-foreground">Cancelamentos</span>
                    <Badge variant={prof.cancellationRate > 20 ? "destructive" : "secondary"}>
                      {prof.cancellationRate}%
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-background rounded-lg">
                    <span className="text-sm text-muted-foreground">Faltas</span>
                    <Badge variant={prof.noShowRate > 15 ? "destructive" : "secondary"}>
                      {prof.noShowRate}%
                    </Badge>
                  </div>
                </div>
              </div>
            ))}

            {professionalStats.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                Nenhum profissional encontrado
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfessionalAnalytics;