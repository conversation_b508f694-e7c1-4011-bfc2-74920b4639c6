# 🚀 Aptor Redis API - Deploy Guide

## 📋 Pré-requisitos

- Docker instalado
- Acesso ao registry (Docker Hub, GitHub Container Registry, etc.)
- Port<PERSON>r configurado
- Rede `agentes-network` criada
- Traefik funcionando

## 🔧 1. Build e Push do Container

### Opção A: Docker Hub
```bash
# Build e push para Docker Hub
./docker-build.sh v1.0.0 seu-usuario-dockerhub

# Exemplo:
./docker-build.sh v1.0.0 datanerd
```

### Opção B: GitHub Container Registry
```bash
# Login no GitHub Container Registry
echo $GITHUB_TOKEN | docker login ghcr.io -u seu-usuario --password-stdin

# Build e push
./docker-build.sh v1.0.0 ghcr.io/seu-usuario
```

### Opção C: Registry Privado
```bash
# Build e push para registry privado
./docker-build.sh v1.0.0 registry.datanerd.com.br/aptor
```

## 🎯 2. Deploy no Portainer

### Passo 1: Atualizar docker-compose.portainer.yml
```yaml
# Edite a linha da imagem:
image: seu-registry/aptor-redis-api:v1.0.0
```

### Passo 2: Deploy via Portainer
1. Acesse Portainer: `https://portainer.datanerd.com.br`
2. Vá em **Stacks** → **Add Stack**
3. Nome: `aptor-redis-api`
4. Cole o conteúdo de `docker-compose.portainer.yml`
5. Configure as variáveis de ambiente se necessário
6. Clique em **Deploy the stack**

### Passo 3: Verificar Deploy
```bash
# Verificar se o container está rodando
docker ps | grep aptor-redis-api

# Verificar logs
docker logs aptor-redis-api

# Testar API
curl https://aptor.datanerd.com.br/api/health
```

## 🔄 3. Atualizações

### Para atualizar a aplicação:

1. **Build nova versão:**
```bash
./docker-build.sh v1.1.0 seu-registry
```

2. **Atualizar no Portainer:**
   - Vá na Stack `aptor-redis-api`
   - Clique em **Editor**
   - Altere a versão da imagem: `v1.1.0`
   - Clique em **Update the stack**

3. **Ou via linha de comando:**
```bash
# Pull nova imagem
docker pull seu-registry/aptor-redis-api:v1.1.0

# Parar container atual
docker stop aptor-redis-api

# Remover container antigo
docker rm aptor-redis-api

# Recriar com nova imagem
docker-compose -f docker-compose.portainer.yml up -d
```

## 🌐 4. URLs de Acesso

Após o deploy, a API estará disponível em:

- **Público**: `https://aptor.datanerd.com.br`
- **Health Check**: `https://aptor.datanerd.com.br/api/health`
- **N8N Sync**: `https://aptor.datanerd.com.br/api/sync/agendamento-whatsapp`
- **Interno (N8N)**: `http://aptor-redis-api:3001`

## 🧪 5. Testes

### Health Check
```bash
curl https://aptor.datanerd.com.br/api/health
```

### Sync Endpoint (N8N)
```bash
curl -X POST https://aptor.datanerd.com.br/api/sync/agendamento-whatsapp \
  -H "Content-Type: application/json" \
  -d '{
    "profissional_id": "prof-123",
    "cliente_nome": "João Silva",
    "cliente_telefone": "11999999999",
    "data_hora": "2025-08-21T14:00:00.000Z",
    "source": "whatsapp"
  }'
```

### Métricas
```bash
curl https://aptor.datanerd.com.br/api/metrics
```

## 🔧 6. Configuração N8N

No seu workflow N8N, use:

**URL**: `http://aptor-redis-api:3001/api/sync/agendamento-whatsapp`
**Method**: `POST`
**Headers**: `Content-Type: application/json`
**Body**:
```json
{
  "profissional_id": "{{ $('HORARIOS E VALORES').item.json.profissional_id }}",
  "cliente_nome": "{{ $('AI Agent').item.json.cliente_nome }}",
  "cliente_telefone": "{{ $('AI Agent').item.json.cliente_telefone }}",
  "data_hora": "{{ $('AI Agent').item.json.data_hora_iso }}",
  "observacoes": "{{ $('AI Agent').item.json.observacoes }}",
  "google_calendar_event_id": "{{ $('[CAL] CADASTRAR EVENTO').item.json.event_id }}",
  "source": "whatsapp"
}
```

## 🚨 7. Troubleshooting

### Container não inicia
```bash
# Verificar logs
docker logs aptor-redis-api

# Verificar configuração
docker inspect aptor-redis-api
```

### Erro de conexão Redis
- Verificar se as credenciais do Redis Cloud estão corretas
- Testar conectividade: `telnet redis-14154.c308.sa-east-1-1.ec2.redns.redis-cloud.com 14154`

### Traefik não roteia
- Verificar se o container está na rede `agentes-network`
- Verificar labels do Traefik
- Verificar DNS: `nslookup aptor.datanerd.com.br`

## 📊 8. Monitoramento

### Logs
```bash
# Logs em tempo real
docker logs -f aptor-redis-api

# Logs das últimas 100 linhas
docker logs --tail 100 aptor-redis-api
```

### Métricas
- Health: `https://aptor.datanerd.com.br/api/health`
- Métricas: `https://aptor.datanerd.com.br/api/metrics`
- Status Redis: Verificar no health check

---

## 🎉 Deploy Completo!

Após seguir estes passos, o Aptor Redis API estará rodando em produção e integrado com seu N8N via `aptor.datanerd.com.br`!
