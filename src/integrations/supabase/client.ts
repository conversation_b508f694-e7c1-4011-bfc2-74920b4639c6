// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://kpqxdptknmpsoswswrpd.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtwcXhkcHRrbm1wc29zd3N3cnBkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQzNDE3MzgsImV4cCI6MjA2OTkxNzczOH0.87rnlYKd1j5WJwG9i-4HMqEqQJtWBe1lFCYTHp7MvBY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});