services:
  # ===========================================
  # PROXY REVERSO E SSL
  # ===========================================

  # Traefik - Proxy Reverso com SSL automático
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    command:
      # API e Dashboard
      - --api.dashboard=true
      - --api.debug=false

      # Providers
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --providers.docker.network=agentes-network

      # Entrypoints
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443

      # Redirect HTTP to HTTPS
      - --entrypoints.web.http.redirections.entrypoint.to=websecure
      - --entrypoints.web.http.redirections.entrypoint.scheme=https

      # Let's Encrypt
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=${ACME_EMAIL:-<EMAIL>}
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
      # Use staging para testar (remova a linha abaixo em produção)
      # - --certificatesresolvers.letsencrypt.acme.caserver=https://acme-staging-v02.api.letsencrypt.org/directory

      # Logs
      - --log.level=INFO
      - --accesslog=true
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik_certs:/letsencrypt
      - traefik_config:/etc/traefik
    networks:
      - agentes-network
    labels: 
         - "traefik.enable=true"
      # Dashboard
      - "traefik.http.routers.traefik-secure.rule=Host(`traefik.${DOMAIN:-datanerd.com.br}`)"
      - "traefik.http.routers.traefik-secure.entrypoints=websecure"
      - "traefik.http.routers.traefik-secure.tls.certresolver=letsencrypt"
      - "traefik.http.routers.traefik-secure.service=api@internal"
      # Middleware de autenticação básica para o dashboard
      - "traefik.http.routers.traefik-secure.middlewares=traefik-auth"
      - "traefik.http.middlewares.traefik-auth.basicauth.users=${TRAEFIK_DASHBOARD_AUTH:-admin:$$2y$$10$$jBbKJCRkL3pJH3LOMCILV.2RF7pFfIjN7OorGaRKh3uzRCQqDuOxa}"

  # ===========================================
  # BANCOS DE DADOS E CACHE
  # ===========================================

  # PostgreSQL - Banco principal
  postgres:
    image: postgres:15-alpine
    container_name: postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - agentes-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 30s
      timeout: 10s
      retries: 5
    labels:
      - "traefik.enable=true"
      # PostgreSQL via SSL (porta 5432)
      - "traefik.tcp.routers.postgres-secure.rule=HostSNI(`postgres.${DOMAIN:-datanerd.com.br}`)"
      - "traefik.tcp.routers.postgres-secure.entrypoints=websecure"
      - "traefik.tcp.routers.postgres-secure.tls=true"
      - "traefik.tcp.routers.postgres-secure.tls.certresolver=letsencrypt"
      - "traefik.tcp.services.postgres-secure.loadbalancer.server.port=5432"

  # Redis - Cache
  redis:
    image: redis:7-alpine
    container_name: redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - agentes-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    labels:
      - "traefik.enable=true"
      # Redis via SSL (porta 6379)
      - "traefik.tcp.routers.redis-secure.rule=HostSNI(`redis.${DOMAIN:-datanerd.com.br}`)"
      - "traefik.tcp.routers.redis-secure.entrypoints=websecure"
      - "traefik.tcp.routers.redis-secure.tls=true"
      - "traefik.tcp.routers.redis-secure.tls.certresolver=letsencrypt"
      - "traefik.tcp.services.redis-secure.loadbalancer.server.port=6379"

  # ===========================================
  # SERVIÇOS PRINCIPAIS
  # ===========================================

  # Evolution API - WhatsApp
  evolution-api:
    image: atendai/evolution-api:latest
    container_name: evolution-api
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # Servidor
      SERVER_TYPE: http
      SERVER_PORT: 8080
      SERVER_URL: https://evolution.${DOMAIN:-datanerd.com.br}

      # CORS
      CORS_ORIGIN: "*"
      CORS_METHODS: "GET,HEAD,PUT,PATCH,POST,DELETE"
      CORS_CREDENTIALS: "true"

      # Logs
      LOG_LEVEL: ERROR,WARN,DEBUG,INFO
      LOG_COLOR: "true"
      LOG_BAILEYS: error

      # Banco de dados - COMUNICAÇÃO INTERNA
      DATABASE_ENABLED: "true"
      DATABASE_PROVIDER: postgresql
      DATABASE_CONNECTION_URI: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}?schema=public
      DATABASE_CONNECTION_CLIENT_NAME: evolution_exchange
      DATABASE_SAVE_DATA_INSTANCE: "true"
      DATABASE_SAVE_DATA_NEW_MESSAGE: "true"
      DATABASE_SAVE_MESSAGE_UPDATE: "true"
      DATABASE_SAVE_DATA_CONTACTS: "true"
      DATABASE_SAVE_DATA_CHATS: "true"
      DATABASE_SAVE_DATA_LABELS: "true"
      DATABASE_SAVE_DATA_HISTORIC: "true"

      # Redis - COMUNICAÇÃO INTERNA
      CACHE_REDIS_ENABLED: "true"
      CACHE_REDIS_URI: redis://:${REDIS_PASSWORD}@redis:6379/6
      CACHE_REDIS_PREFIX_KEY: evolution

      # Autenticação
      AUTHENTICATION_TYPE: apikey
      AUTHENTICATION_API_KEY: ${EVOLUTION_API_KEY}
      AUTHENTICATION_GLOBAL_AUTH_TOKEN: ${EVOLUTION_GLOBAL_AUTH_TOKEN}
      AUTHENTICATION_EXPOSE_IN_FETCH_INSTANCES: "true"

      # Webhook para n8n - COMUNICAÇÃO INTERNA
      WEBHOOK_GLOBAL_ENABLED: "true"
      WEBHOOK_GLOBAL_URL: http://n8n:5678/webhook/evolution
      WEBHOOK_EVENTS_APPLICATION_STARTUP: "true"
      WEBHOOK_EVENTS_QRCODE_UPDATED: "true"
      WEBHOOK_EVENTS_MESSAGES_UPSERT: "true"
      WEBHOOK_EVENTS_MESSAGES_UPDATE: "true"
      WEBHOOK_EVENTS_MESSAGES_DELETE: "true"
      WEBHOOK_EVENTS_SEND_MESSAGE: "true"
      WEBHOOK_EVENTS_CONTACTS_UPSERT: "true"
      WEBHOOK_EVENTS_PRESENCE_UPDATE: "true"
      WEBHOOK_EVENTS_CHATS_UPSERT: "true"
      WEBHOOK_EVENTS_CHATS_UPDATE: "true"
      WEBHOOK_EVENTS_CHATS_DELETE: "true"
      WEBHOOK_EVENTS_GROUPS_UPSERT: "true"
      WEBHOOK_EVENTS_CONNECTION_UPDATE: "true"

      # Configurações da instância
      CONFIG_SESSION_PHONE_CLIENT: "Evolution API"
      CONFIG_SESSION_PHONE_NAME: "Chrome"
      CONFIG_SESSION_PHONE_VERSION: "2.3000.1023337412"

      DEL_INSTANCE: "false"

    volumes:
      - evolution_instances:/evolution/instances
      - evolution_store:/evolution/store
    networks:
      - agentes-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.evolution-secure.rule=Host(`evolution.${DOMAIN:-datanerd.com.br}`)"
      - "traefik.http.routers.evolution-secure.entrypoints=websecure"
      - "traefik.http.routers.evolution-secure.tls.certresolver=letsencrypt"
      - "traefik.http.services.evolution-secure.loadbalancer.server.port=8080"

  # n8n - Automação
  n8n:
    image: n8nio/n8n:1.107.4
    container_name: n8n
    restart: unless-stopped
    environment:
      N8N_HOST: 0.0.0.0
      N8N_PORT: 5678
      N8N_PROTOCOL: https
      N8N_EDITOR_BASE_URL: https://automation.${DOMAIN:-datanerd.com.br}

      # Configurações
      N8N_USER_MANAGEMENT_DISABLED: "true"
      N8N_DIAGNOSTICS_ENABLED: "false"
      N8N_TEMPLATES_ENABLED: "true"
      N8N_PUBLIC_API_DISABLED: "false"
      N8N_SECURE_COOKIE: "true"

      # Webhook - URL pública para webhooks externos
      WEBHOOK_URL: https://automation.${DOMAIN:-datanerd.com.br}/

      # Configuração para permitir IPs internos
      N8N_BLOCK_ENV_ACCESS_IN_NODE: "false"
      N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN: "true"

      # Execução
      EXECUTIONS_MODE: regular
      EXECUTIONS_TIMEOUT: 3600
      EXECUTIONS_DATA_SAVE_ON_ERROR: all
      EXECUTIONS_DATA_SAVE_ON_SUCCESS: all

      # Timezone
      GENERIC_TIMEZONE: America/Sao_Paulo
      TZ: America/Sao_Paulo

      # Logs
      N8N_LOG_LEVEL: info

      # Database connection
      DATABASE_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${POSTGRES_DB}
      DB_USER: ${POSTGRES_USER}
      DB_PASSWORD: ${POSTGRES_PASSWORD}
      N8N_LOG_OUTPUT: console

    volumes:
      - n8n_data:/home/<USER>/.n8n
    networks:
      - agentes-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.n8n-secure.rule=Host(`automation.${DOMAIN:-datanerd.com.br}`)"
      - "traefik.http.routers.n8n-secure.entrypoints=websecure"
      - "traefik.http.routers.n8n-secure.tls.certresolver=letsencrypt"
      - "traefik.http.services.n8n-secure.loadbalancer.server.port=5678"

  # ChromaDB - Banco vetorial
  #chromadb:
  #  image: chromadb/chroma:latest
  #  container_name: chromadb
  #  restart: unless-stopped
  #  environment:
  #    CHROMA_DB_IMPL: duckdb+parquet
  #    CHROMA_SERVER_HOST: 0.0.0.0
  #    CHROMA_SERVER_PORT: 8000
  #    CHROMA_SERVER_AUTH_PROVIDER: ${CHROMA_AUTH_PROVIDER:-token}
  #    CHROMA_SERVER_AUTH_TOKEN_TRANSPORT_HEADER: ${CHROMA_AUTH_HEADER:-X-Chroma-Token}
  #    CHROMA_SERVER_AUTH_CREDENTIALS: ${CHROMADB_AUTH_TOKEN:-test-token}
  #  volumes:
  #    - chromadb_data:/chromadb
  #  networks:
  #    - agentes-network
  #  labels:
  #    - "traefik.enable=true"
  #    - "traefik.http.routers.chromadb-secure.rule=Host(`chromadb.${DOMAIN:-datanerd.com.br}`)"
  #    - "traefik.http.routers.chromadb-secure.entrypoints=websecure"
  #    - "traefik.http.routers.chromadb-secure.tls.certresolver=letsencrypt"
  #    - "traefik.http.services.chromadb-secure.loadbalancer.server.port=8000"
  #    # Autenticação adicional via Traefik (opcional)
  #    - "traefik.http.routers.chromadb-secure.middlewares=chromadb-auth"
  #    - "traefik.http.middlewares.chromadb-auth.basicauth.users=${CHROMADB_BASIC_AUTH:-admin:$$2y$$10$$jBbKJCRkL3pJH3LOMCILV.2RF7pFfIjN7OorGaRKh3uzRCQqDuOxa}"

  # Ollama - LLM local
  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    restart: unless-stopped
    environment:
      OLLAMA_HOST: 0.0.0.0
      OLLAMA_ORIGINS: "*"
    volumes:
     - ollama_data:/root/.ollama
    networks:
      - agentes-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ollama-secure.rule=Host(`ollama.${DOMAIN:-datanerd.com.br}`)"
      - "traefik.http.routers.ollama-secure.entrypoints=websecure"
      - "traefik.http.routers.ollama-secure.tls.certresolver=letsencrypt"
      - "traefik.http.services.ollama-secure.loadbalancer.server.port=11434"
      # Autenticação básica para Ollama
      - "traefik.http.routers.ollama-secure.middlewares=ollama-auth"
      - "traefik.http.middlewares.ollama-auth.basicauth.users=${OLLAMA_AUTH:-admin:$$2y$$10$$jBbKJCRkL3pJH3LOMCILV.2RF7pFfIjN7OorGaRKh3uzRCQqDuOxa}"

  # API Node.js - Lógica customizada
  api-node:
    image: node:18-alpine
    container_name: api-node
    restart: unless-stopped
    working_dir: /app
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      NODE_ENV: production
      PORT: 3000
      # Conexões INTERNAS (via rede Docker)
      DATABASE_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${POSTGRES_DB}
      DB_USER: ${POSTGRES_USER}
      DB_PASSWORD: ${POSTGRES_PASSWORD}
      CHROMADB_URL: http://chromadb:8000
      CHROMADB_TOKEN: ${CHROMADB_AUTH_TOKEN}
      EVOLUTION_API_URL: http://evolution-api:8080
      EVOLUTION_API_KEY: ${EVOLUTION_API_KEY}
      N8N_WEBHOOK_URL: http://n8n:5678
      OLLAMA_URL: http://ollama:11434
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      # URLs públicas apenas para callbacks externos
      API_PUBLIC_URL: https://api.${DOMAIN:-datanerd.com.br}
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN}
    volumes:
      - ./api-node:/app
    command: sh -c "npm install && npm start"
    networks:
      - agentes-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api-secure.rule=Host(`api.${DOMAIN:-datanerd.com.br}`)"
      - "traefik.http.routers.api-secure.entrypoints=websecure"
      - "traefik.http.routers.api-secure.tls.certresolver=letsencrypt"
      - "traefik.http.services.api-secure.loadbalancer.server.port=3000"
      # CORS headers
      - "traefik.http.middlewares.api-cors.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolallowheaders=*"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolalloworiginlist=*"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.api-cors.headers.addvaryheader=true"
      - "traefik.http.routers.api-secure.middlewares=api-cors"

  # Aptor App
  aptor:
    image: datanerd/automation:aptor-complete-v2.2.0
    container_name: aptor
    restart: unless-stopped
    depends_on:
      - traefik

    environment:
      NODE_ENV: production
      SERVER_PORT: 3001
      LOG_LEVEL: info

      # Database connection (PostgreSQL local - usado por outros serviços)
      DATABASE_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${POSTGRES_DB}
      DB_USER: ${POSTGRES_USER}
      DB_PASSWORD: ${POSTGRES_PASSWORD}

      # Redis Cloud EXTERNO - EXCLUSIVO DO APTOR
      REDIS_HOST: ${REDIS_HOST}
      REDIS_PORT: ${REDIS_PORT}
      REDIS_USERNAME: ${REDIS_USERNAME}
      REDIS_PASSWORD: ${REDIS_PASSWORD}

      # Supabase - Autenticação
      SUPABASE_URL: ${SUPABASE_URL}
      SUPABASE_SERVICE_KEY: ${SUPABASE_SERVICE_KEY}
      SUPABASE_DATABASE_URL: ${SUPABASE_DATABASE_URL}

      # JWT e Better Auth
      JWT_SECRET: ${JWT_SECRET}
      BETTER_AUTH_SECRET: ${BETTER_AUTH_SECRET}
      BETTER_AUTH_URL: https://aptor.${DOMAIN:-datanerd.com.br}

    volumes:
      - aptor_logs:/app/logs

    networks:
      - agentes-network

    healthcheck:
      # ← MUDANÇA: Verificar tanto frontend (/) quanto backend (/api/health)
      test: ["CMD", "sh", "-c", "wget --no-verbose --tries=1 --spider http://localhost:3001/ && wget --no-verbose --tries=1 --spider http://localhost:3001/api/health"]
      interval: 30s
      timeout: 15s  # ← MUDANÇA: Mais tempo
      retries: 3
      start_period: 45s  # ← MUDANÇA: Mais tempo para nginx + backend iniciarem

    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.aptor-secure.rule=Host(`aptor.${DOMAIN:-datanerd.com.br}`)"
      - "traefik.http.routers.aptor-secure.entrypoints=websecure"
      - "traefik.http.routers.aptor-secure.tls=true"
      - "traefik.http.routers.aptor-secure.tls.certresolver=letsencrypt"
      - "traefik.http.routers.aptor-secure.service=aptor-service"
      - "traefik.http.services.aptor-service.loadbalancer.server.port=3001"  # ← CONTINUA 3001 (NGINX)
      - "traefik.http.middlewares.aptor-cors.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE,PATCH"
      - "traefik.http.middlewares.aptor-cors.headers.accesscontrolallowheaders=*"
      - "traefik.http.middlewares.aptor-cors.headers.accesscontrolalloworiginlist=*"
      - "traefik.http.middlewares.aptor-cors.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.aptor-cors.headers.addvaryheader=true"
      - "traefik.http.routers.aptor-secure.middlewares=aptor-cors"

  # ===========================================
  # FERRAMENTAS DE ADMINISTRAÇÃO
  # ===========================================

  # pgAdmin - Interface gráfica PostgreSQL
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-aMvfp8TVcbnkEt6X}
      PGADMIN_CONFIG_SERVER_MODE: 'True'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - agentes-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.pgadmin-secure.rule=Host(`pgadmin.${DOMAIN:-datanerd.com.br}`)"
      - "traefik.http.routers.pgadmin-secure.entrypoints=websecure"
      - "traefik.http.routers.pgadmin-secure.tls.certresolver=letsencrypt"
      - "traefik.http.services.pgadmin-secure.loadbalancer.server.port=80"

  # Portainer - Gerenciamento de containers
  portainer:
    image: portainer/portainer-ce:2.33.0
    container_name: portainer
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    networks:
      - agentes-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.portainer-secure.rule=Host(`portainer.${DOMAIN:-datanerd.com.br}`)"
      - "traefik.http.routers.portainer-secure.entrypoints=websecure"
      - "traefik.http.routers.portainer-secure.tls.certresolver=letsencrypt"
      - "traefik.http.services.portainer-secure.loadbalancer.server.port=9000"

volumes:
  postgres_data:
  redis_data:
  evolution_instances:
  evolution_store:
  n8n_data:
  #chromadb_data:
  ollama_data:
  pgadmin_data:
  portainer_data:
  traefik_certs:
  traefik_config:
  aptor_logs:

networks:
  agentes-network:
    driver: bridge                    