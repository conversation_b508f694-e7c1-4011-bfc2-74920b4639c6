# Aptor Complete App - Frontend + Backend Container
FROM --platform=linux/amd64 node:18-alpine AS builder

# Install build dependencies
RUN apk add --no-cache curl

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install ALL dependencies (including dev dependencies for build)
RUN npm ci --no-audit --no-fund

# Copy source code
COPY . .

# Build frontend
RUN npm run build

# Production stage
FROM --platform=linux/amd64 node:18-alpine AS production

# Metadata
LABEL maintainer="DataNerd <<EMAIL>>"
LABEL description="Aptor Complete - Frontend + Backend com Redis"
LABEL version="1.0.0"

# Install curl for health check
RUN apk add --no-cache curl

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S aptor -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Copy package files and install only production dependencies
COPY package*.json ./
RUN npm ci --only=production --no-audit --no-fund && \
    npm cache clean --force

# Copy backend from builder
COPY --from=builder /app/server ./server

# Copy built frontend from builder
COPY --from=builder /app/dist ./public

# No nginx needed - Node.js will serve static files directly

# Create logs directory
RUN mkdir -p /app/logs && chown -R aptor:nodejs /app

# Switch to non-root user
USER aptor

# Environment variables
ENV NODE_ENV=production
ENV SERVER_PORT=3001
ENV LOG_LEVEL=info

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
  CMD curl -f http://localhost:3001/api/health || exit 1

# Start the application
CMD ["node", "server/index.js"]
