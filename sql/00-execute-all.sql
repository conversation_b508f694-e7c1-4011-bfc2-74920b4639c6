-- ===========================================
-- APTOR - SCRIPT COMPLETO DE INSTALAÇÃO
-- Execute este SQL para instalar tudo de uma vez
-- CUIDADO: Use apenas em ambiente novo/desenvolvimento
-- ===========================================

-- Verificar se é seguro executar
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM users LIMIT 1) THEN
        RAISE EXCEPTION 'ATENÇÃO: Já existem usuários no sistema. Execute os scripts individuais para evitar perda de dados.';
    END IF;
    
    RAISE NOTICE '===========================================';
    RAISE NOTICE 'INICIANDO INSTALAÇÃO COMPLETA DO APTOR';
    RAISE NOTICE '===========================================';
END $$;

-- ===========================================
-- SCRIPT 1: ESTRUTURA BASE
-- ===========================================

\echo 'Executando Script 1: Criando estrutura base...'

-- Extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Tabela de usuários (Better Auth)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    name VARCHAR(255) NOT NULL,
    image TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de contas (Better Auth)
CREATE TABLE IF NOT EXISTS accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    account_id VARCHAR(255) NOT NULL,
    provider_id VARCHAR(255) NOT NULL,
    access_token TEXT,
    refresh_token TEXT,
    id_token TEXT,
    access_token_expires_at TIMESTAMP WITH TIME ZONE,
    refresh_token_expires_at TIMESTAMP WITH TIME ZONE,
    scope TEXT,
    password TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(provider_id, account_id)
);

-- Tabela de sessões (Better Auth)
CREATE TABLE IF NOT EXISTS sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de tokens de verificação (Better Auth)
CREATE TABLE IF NOT EXISTS verification_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    identifier VARCHAR(255) NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela customizada para dados adicionais
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'profissional' CHECK (role IN ('superadmin', 'admin', 'profissional', 'inactive')),
    phone VARCHAR(20),
    avatar TEXT,
    specialty VARCHAR(100),
    bio TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    deactivated_at TIMESTAMP WITH TIME ZONE,
    deactivated_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Tabela de logs de auditoria
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    resource_id VARCHAR(255),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

\echo 'Script 1 concluído: Estrutura base criada'

-- ===========================================
-- SCRIPT 2: ÍNDICES E PERFORMANCE
-- ===========================================

\echo 'Executando Script 2: Criando índices...'

-- Índices principais
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON user_profiles(role);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);

-- Função para updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_accounts_updated_at BEFORE UPDATE ON accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

\echo 'Script 2 concluído: Índices criados'

-- ===========================================
-- SCRIPT 3: SEGURANÇA (SIMPLIFICADO)
-- ===========================================

\echo 'Executando Script 3: Configurando segurança...'

-- Habilitar RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Políticas básicas (simplificadas para instalação rápida)
CREATE POLICY "users_all" ON users FOR ALL USING (true);
CREATE POLICY "profiles_all" ON user_profiles FOR ALL USING (true);
CREATE POLICY "logs_all" ON audit_logs FOR ALL USING (true);

\echo 'Script 3 concluído: Segurança configurada'

-- ===========================================
-- SCRIPT 4: CRIAR SUPERADMIN
-- ===========================================

\echo 'Executando Script 4: Criando SuperAdmin...'

DO $$
DECLARE
    superadmin_id UUID;
    password_hash TEXT;
BEGIN
    password_hash := crypt('SuperAdmin@2025', gen_salt('bf', 10));
    
    INSERT INTO users (id, email, name, email_verified) 
    VALUES (
        uuid_generate_v4(),
        '<EMAIL>',
        'SuperAdmin Aptor',
        TRUE
    ) RETURNING id INTO superadmin_id;
    
    INSERT INTO user_profiles (user_id, role, specialty, is_active, created_by)
    VALUES (superadmin_id, 'superadmin', 'Administração do Sistema', TRUE, superadmin_id);
        
    INSERT INTO accounts (user_id, account_id, provider_id, password)
    VALUES (superadmin_id, '<EMAIL>', 'credential', password_hash);
    
    RAISE NOTICE 'SuperAdmin criado: <EMAIL> / SuperAdmin@2025';
END $$;

\echo 'Script 4 concluído: SuperAdmin criado'

-- ===========================================
-- VERIFICAÇÃO FINAL
-- ===========================================

\echo 'Verificando instalação...'

SELECT 
    'INSTALAÇÃO CONCLUÍDA COM SUCESSO!' as status,
    (SELECT COUNT(*) FROM users) as total_usuarios,
    (SELECT COUNT(*) FROM user_profiles WHERE role = 'superadmin') as superadmins,
    NOW() as instalado_em;

SELECT 
    'CREDENCIAIS DO SUPERADMIN:' as info,
    '<EMAIL>' as email,
    'SuperAdmin@2025' as senha,
    'ALTERE A SENHA APÓS PRIMEIRO LOGIN!' as importante;

\echo '==========================================='
\echo 'INSTALAÇÃO COMPLETA DO APTOR FINALIZADA!'
\echo 'Email: <EMAIL>'
\echo 'Senha: SuperAdmin@2025'
\echo 'ALTERE A SENHA APÓS O PRIMEIRO LOGIN!'
\echo '==========================================='
