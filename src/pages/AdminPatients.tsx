import { useState } from "react";
import { Search, Plus, Edit, Trash2, Phone, Mail, Calendar, CheckCircle, XCircle, Filter, SortAsc, Download, UserPlus, Users } from "lucide-react";
import EditClientModal from "@/components/modals/EditClientModal";
import DeleteClientModal from "@/components/modals/DeleteClientModal";
import ClientHistoryModal from "@/components/modals/ClientHistoryModal";
import PresenceControl from "@/components/ui/presence-control";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Pop<PERSON>, <PERSON>overContent, PopoverTrigger } from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useQuery } from "@tanstack/react-query";
import { clienteService, agendamentoService, type Cliente } from "@/services/database";

const AdminPatients = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [presenceFilter, setPresenceFilter] = useState("all");
  const [sortBy, setSortBy] = useState("name");
  const [sortOrder, setSortOrder] = useState("asc");

  // Modal states
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [historyModalOpen, setHistoryModalOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Cliente | null>(null);

  // Fetch clients data
  const { data: clients = [] } = useQuery({
    queryKey: ['clients'],
    queryFn: clienteService.getAll
  });

  // Fetch appointments for visit counts
  const { data: appointments = [] } = useQuery({
    queryKey: ['appointments'],
    queryFn: agendamentoService.getAll
  });

  // Transform clients data
  const patients = clients.map(client => {
    const clientAppointments = appointments.filter(apt => apt.cliente_id === client.id);
    const lastVisit = clientAppointments
      .sort((a, b) => new Date(b.data_hora).getTime() - new Date(a.data_hora).getTime())[0];
    
    // Check if client is inactive (>30 days since last appointment)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const isInactive = lastVisit ? new Date(lastVisit.data_hora) < thirtyDaysAgo : true;
    
    return {
      id: client.id,
      name: client.nome || 'Nome não informado',
      email: client.email || 'Email não informado',
      phone: client.numero_whatsapp,
      lastVisit: lastVisit ? new Date(lastVisit.data_hora).toISOString().split('T')[0] : null,
      totalVisits: clientAppointments.length,
      status: isInactive ? 'inactive' : 'active',
      presenceStatus: client.presenca_status || 'pending'
    };
  });

  // Enhanced filtering and sorting
  const filteredAndSortedPatients = patients
    .filter(patient => {
      const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        patient.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        patient.phone.includes(searchTerm);
      
      const matchesStatus = statusFilter === "all" || patient.status === statusFilter;
      const matchesPresence = presenceFilter === "all" || patient.presenceStatus === presenceFilter;
      
      return matchesSearch && matchesStatus && matchesPresence;
    })
    .sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case "name":
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case "lastVisit":
          aValue = a.lastVisit ? new Date(a.lastVisit).getTime() : 0;
          bValue = b.lastVisit ? new Date(b.lastVisit).getTime() : 0;
          break;
        case "totalVisits":
          aValue = a.totalVisits;
          bValue = b.totalVisits;
          break;
        default:
          return 0;
      }
      
      const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      return sortOrder === "asc" ? comparison : -comparison;
    });

  const stats = {
    total: patients.length,
    active: patients.filter(p => p.status === "active").length,
    inactive: patients.filter(p => p.status === "inactive").length,
    newThisMonth: 2
  };

  const getStatusColor = (status: string) => {
    return status === "active"
      ? "bg-success text-success-foreground"
      : "bg-muted text-muted-foreground";
  };

  const getStatusText = (status: string) => {
    return status === "active" ? "Ativo" : "Inativo";
  };

  // Modal handlers
  const handleEditClient = (client: Cliente) => {
    setSelectedClient(client);
    setEditModalOpen(true);
  };

  const handleDeleteClient = (client: Cliente) => {
    setSelectedClient(client);
    setDeleteModalOpen(true);
  };

  const handleViewHistory = (client: Cliente) => {
    setSelectedClient(client);
    setHistoryModalOpen(true);
  };

  const closeModals = () => {
    setEditModalOpen(false);
    setDeleteModalOpen(false);
    setHistoryModalOpen(false);
    setSelectedClient(null);
  };

  return (
    <div className="min-h-screen bg-gradient-secondary">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 animate-fade-in-up">
          <div>
            <h1 className="text-3xl font-bold text-primary mb-2">Clientes</h1>
            <p className="text-muted-foreground">
              Gerencie seus clientes e histórico de atendimentos
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </Button>
            <Button variant="outline">
              <UserPlus className="h-4 w-4 mr-2" />
              Importar
            </Button>
            <Button className="bg-gradient-primary shadow-elegant">
              <Plus className="h-4 w-4 mr-2" />
              Novo Cliente
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total de Clientes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">{stats.total}</div>
            </CardContent>
          </Card>

          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Clientes Ativos</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-success">{stats.active}</div>
            </CardContent>
          </Card>

          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Clientes Inativos</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-muted-foreground">{stats.inactive}</div>
            </CardContent>
          </Card>

          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Novos (Este Mês)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">{stats.newThisMonth}</div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Search and Filter */}
        <Card className="shadow-card mb-8 animate-fade-in-up" style={{ animationDelay: '0.5s' }}>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Filtros e Busca</span>
            </CardTitle>
            <CardDescription>
              Use filtros avançados para encontrar clientes específicos
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
              {/* Search */}
              <div className="relative lg:col-span-2">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar clientes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              {/* Status Filter */}
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os Status</SelectItem>
                  <SelectItem value="active">Ativos</SelectItem>
                  <SelectItem value="inactive">Inativos</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Presence Filter */}
              <Select value={presenceFilter} onValueChange={setPresenceFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Presença" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as Presenças</SelectItem>
                  <SelectItem value="show">Veio</SelectItem>
                  <SelectItem value="no_show">Faltou</SelectItem>
                  <SelectItem value="pending">Pendente</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Sort By */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger>
                  <SelectValue placeholder="Ordenar por" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Nome</SelectItem>
                  <SelectItem value="lastVisit">Última Consulta</SelectItem>
                  <SelectItem value="totalVisits">Total de Consultas</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Sort Order */}
              <Button
                variant="outline"
                onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
                className="flex items-center space-x-2"
              >
                <SortAsc className={`h-4 w-4 ${sortOrder === "desc" ? "rotate-180" : ""} transition-transform`} />
                <span>{sortOrder === "asc" ? "Crescente" : "Decrescente"}</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Patients Table */}
        <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
          <CardHeader>
            <CardTitle>Lista de Pacientes</CardTitle>
            <CardDescription>
              {filteredAndSortedPatients.length} paciente(s) encontrado(s) de {patients.length} total
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border overflow-hidden">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="min-w-[200px]">Paciente</TableHead>
                      <TableHead className="hidden md:table-cell min-w-[200px]">Contato</TableHead>
                      <TableHead className="hidden lg:table-cell">Última Consulta</TableHead>
                      <TableHead className="hidden sm:table-cell">Total</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="hidden md:table-cell">Presença</TableHead>
                      <TableHead className="text-right min-w-[120px]">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAndSortedPatients.length > 0 ? filteredAndSortedPatients.map((patient, index) => (
                    <TableRow 
                      key={patient.id}
                      className="animate-fade-in-up"
                      style={{ animationDelay: `${0.7 + index * 0.1}s` }}
                    >
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-8 w-8 md:h-10 md:w-10">
                            <AvatarImage src="" />
                            <AvatarFallback className="bg-primary text-primary-foreground text-xs md:text-sm">
                              {patient.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div className="min-w-0 flex-1">
                            <div className="font-medium truncate">{patient.name}</div>
                            <div className="text-xs text-muted-foreground md:hidden">
                              {patient.phone}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2 text-sm">
                            <Mail className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                            <span className="truncate">{patient.email}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm">
                            <Phone className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                            <span>{patient.phone}</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{patient.lastVisit ? new Date(patient.lastVisit).toLocaleDateString('pt-BR') : 'Nunca'}</span>
                        </div>
                      </TableCell>
                      <TableCell className="hidden sm:table-cell">
                        <Badge variant="secondary" className="font-medium text-xs">
                          {patient.totalVisits}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <Badge className={`${getStatusColor(patient.status)} text-xs`}>
                            {getStatusText(patient.status)}
                          </Badge>
                          <div className="md:hidden">
                            {patient.presenceStatus === 'show' && (
                              <Badge className="bg-success text-success-foreground text-xs">
                                <CheckCircle className="h-2 w-2 mr-1" />
                                Veio
                              </Badge>
                            )}
                            {patient.presenceStatus === 'no_show' && (
                              <Badge className="bg-destructive text-destructive-foreground text-xs">
                                <XCircle className="h-2 w-2 mr-1" />
                                Faltou
                              </Badge>
                            )}
                            {patient.presenceStatus === 'pending' && (
                              <Badge variant="outline" className="text-xs">
                                Pendente
                              </Badge>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        <PresenceControl
                          client={clients.find(c => c.id === patient.id)!}
                          size="sm"
                          showLabel={true}
                        />
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-1">
                          <Button
                            size="sm"
                            variant="outline"
                            title="Editar Cliente"
                            className="h-8 w-8 p-0"
                            onClick={() => handleEditClient(clients.find(c => c.id === patient.id)!)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            title="Ver Histórico"
                            className="hidden sm:flex h-8 w-8 p-0"
                            onClick={() => handleViewHistory(clients.find(c => c.id === patient.id)!)}
                          >
                            <Calendar className="h-3 w-3" />
                          </Button>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button size="sm" variant="outline" className="h-8 w-8 p-0 sm:hidden">
                                <span className="sr-only">Mais opções</span>
                                <span className="text-xs">⋯</span>
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-40 p-0" align="end">
                              <div className="flex flex-col">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="justify-start h-8"
                                  onClick={() => handleViewHistory(clients.find(c => c.id === patient.id)!)}
                                >
                                  <Calendar className="h-3 w-3 mr-2" />
                                  Histórico
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="justify-start h-8 text-destructive"
                                  onClick={() => handleDeleteClient(clients.find(c => c.id === patient.id)!)}
                                >
                                  <Trash2 className="h-3 w-3 mr-2" />
                                  Excluir
                                </Button>
                              </div>
                            </PopoverContent>
                          </Popover>
                        </div>
                      </TableCell>
                    </TableRow>
                    )) : (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                          Nenhum cliente encontrado com os filtros aplicados
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Modals */}
        <EditClientModal
          client={selectedClient}
          isOpen={editModalOpen}
          onClose={closeModals}
        />

        <DeleteClientModal
          client={selectedClient}
          isOpen={deleteModalOpen}
          onClose={closeModals}
        />

        <ClientHistoryModal
          client={selectedClient}
          isOpen={historyModalOpen}
          onClose={closeModals}
        />
      </div>
    </div>
  );
};

export default AdminPatients;