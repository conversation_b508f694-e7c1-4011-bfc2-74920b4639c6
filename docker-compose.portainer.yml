# Aptor Redis API - Para uso no Portainer
# Este arquivo deve ser usado no Portainer para deploy do Aptor

version: '3.8'

services:
  aptor-redis-api:
    image: datanerd/automation:aptor-redis-api
    container_name: aptor-redis-api
    restart: unless-stopped
    
    environment:
      # Server Configuration
      NODE_ENV: production
      SERVER_PORT: 3001
      LOG_LEVEL: info
      
      # Redis Cloud Configuration (EXTERNO)
      REDIS_HOST: redis-14154.c308.sa-east-1-1.ec2.redns.redis-cloud.com
      REDIS_PORT: 14154
      REDIS_USERNAME: hordak
      REDIS_PASSWORD: 3B4q!t#T8xuvPDf
      
      # API Metadata
      API_VERSION: 1.0.0
      API_NAME: Aptor Redis API
    
    volumes:
      - aptor_logs:/app/logs
    
    networks:
      - agentes-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s
    
    labels:
      # Traefik Configuration
      - "traefik.enable=true"
      - "traefik.docker.network=agentes-network"
      
      # HTTP Router
      - "traefik.http.routers.aptor-secure.rule=Host(`aptor.${DOMAIN:-datanerd.com.br}`)"
      - "traefik.http.routers.aptor-secure.entrypoints=websecure"
      - "traefik.http.routers.aptor-secure.tls.certresolver=letsencrypt"
      - "traefik.http.services.aptor-secure.loadbalancer.server.port=3001"
      
      # CORS Middleware para N8N
      - "traefik.http.middlewares.aptor-cors.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE,PATCH"
      - "traefik.http.middlewares.aptor-cors.headers.accesscontrolallowheaders=*"
      - "traefik.http.middlewares.aptor-cors.headers.accesscontrolalloworiginlist=*"
      - "traefik.http.middlewares.aptor-cors.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.aptor-cors.headers.addvaryheader=true"
      - "traefik.http.routers.aptor-secure.middlewares=aptor-cors"
      
      # Container Labels
      - "com.datanerd.service=aptor-redis-api"
      - "com.datanerd.version=1.0.0"
      - "com.datanerd.description=Sistema de Agendamento com Cache Redis"

volumes:
  aptor_logs:
    driver: local

networks:
  agentes-network:
    external: true  # Usa a rede existente do seu docker-compose principal
