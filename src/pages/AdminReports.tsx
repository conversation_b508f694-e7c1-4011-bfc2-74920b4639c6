import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";
import { agendamentoService, clienteService } from "@/services/database";
import { FileText, TrendingUp, Users, Calendar, Download, Filter } from "lucide-react";

const AdminReports = () => {
  const [timeRange, setTimeRange] = useState("30"); // days
  const [reportType, setReportType] = useState("overview");

  // Fetch data
  const { data: appointments = [], isLoading: appointmentsLoading } = useQuery({
    queryKey: ['appointments'],
    queryFn: agendamentoService.getAll
  });

  const { data: clients = [], isLoading: clientsLoading } = useQuery({
    queryKey: ['clients'],
    queryFn: clienteService.getAll
  });

  const isLoading = appointmentsLoading || clientsLoading;

  // Calculate date range
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - parseInt(timeRange));

  // Filter appointments by date range
  const filteredAppointments = appointments.filter(apt => {
    const aptDate = new Date(apt.data_hora);
    return aptDate >= startDate && aptDate <= endDate;
  });

  // Calculate statistics
  const totalAppointments = filteredAppointments.length;
  const completedAppointments = filteredAppointments.filter(apt => 
    apt.status === 'confirmado' || apt.status === 'concluído'
  ).length;
  const noShowAppointments = filteredAppointments.filter(apt => 
    apt.status === 'ausente' || apt.status === 'no_show'
  ).length;
  const cancelledAppointments = filteredAppointments.filter(apt => 
    apt.status === 'cancelado'
  ).length;

  const showRate = totalAppointments > 0 ? ((completedAppointments / totalAppointments) * 100).toFixed(1) : "0";
  const noShowRate = totalAppointments > 0 ? ((noShowAppointments / totalAppointments) * 100).toFixed(1) : "0";

  // Prepare chart data
  const statusData = [
    { name: 'Compareceram', value: completedAppointments, color: '#10b981' },
    { name: 'Faltaram', value: noShowAppointments, color: '#ef4444' },
    { name: 'Cancelaram', value: cancelledAppointments, color: '#f59e0b' },
    { name: 'Agendados', value: filteredAppointments.filter(apt => apt.status === 'agendado').length, color: '#6b7280' }
  ];

  // Monthly trend data
  const monthlyData = [];
  for (let i = 5; i >= 0; i--) {
    const date = new Date();
    date.setMonth(date.getMonth() - i);
    const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
    const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    
    const monthAppointments = appointments.filter(apt => {
      const aptDate = new Date(apt.data_hora);
      return aptDate >= monthStart && aptDate <= monthEnd;
    });

    const monthName = date.toLocaleDateString('pt-BR', { month: 'short' });
    
    monthlyData.push({
      month: monthName,
      total: monthAppointments.length,
      completed: monthAppointments.filter(apt => apt.status === 'confirmado' || apt.status === 'concluído').length,
      noShow: monthAppointments.filter(apt => apt.status === 'ausente' || apt.status === 'no_show').length
    });
  }

  // Client activity data
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const activeClients = clients.filter(client => {
    const lastAppointment = appointments
      .filter(apt => apt.cliente_id === client.id)
      .sort((a, b) => new Date(b.data_hora).getTime() - new Date(a.data_hora).getTime())[0];
    
    return lastAppointment && new Date(lastAppointment.data_hora) >= thirtyDaysAgo;
  }).length;

  const inactiveClients = clients.length - activeClients;

  const clientActivityData = [
    { name: 'Ativos (30 dias)', value: activeClients, color: '#10b981' },
    { name: 'Inativos (+30 dias)', value: inactiveClients, color: '#ef4444' }
  ];

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Carregando relatórios...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-primary mb-2">Relatórios de Clientes</h1>
          <p className="text-muted-foreground">Análise detalhada de atendimentos e comportamento dos clientes</p>
        </div>

        <div className="flex items-center space-x-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Últimos 7 dias</SelectItem>
              <SelectItem value="30">Últimos 30 dias</SelectItem>
              <SelectItem value="90">Últimos 90 dias</SelectItem>
              <SelectItem value="365">Último ano</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Atendimentos</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalAppointments}</div>
            <p className="text-xs text-muted-foreground">
              Últimos {timeRange} dias
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Comparecimento</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{showRate}%</div>
            <p className="text-xs text-muted-foreground">
              {completedAppointments} de {totalAppointments} atendimentos
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Faltas</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{noShowRate}%</div>
            <p className="text-xs text-muted-foreground">
              {noShowAppointments} faltas registradas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Clientes Ativos</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeClients}</div>
            <p className="text-xs text-muted-foreground">
              Últimos 30 dias
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Data Summary */}
      <div className="grid gap-6 md:grid-cols-2 mb-8">
        {/* Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Distribuição de Status</CardTitle>
            <CardDescription>
              Últimos {timeRange} dias
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {statusData.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-4 h-4 rounded"
                      style={{ backgroundColor: item.color }}
                    />
                    <span>{item.name}</span>
                  </div>
                  <Badge variant="outline">{item.value}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Client Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Atividade dos Clientes</CardTitle>
            <CardDescription>
              Baseado nos últimos 30 dias
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {clientActivityData.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-4 h-4 rounded"
                      style={{ backgroundColor: item.color }}
                    />
                    <span>{item.name}</span>
                  </div>
                  <Badge variant="outline">{item.value}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Trend */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Tendência Mensal</CardTitle>
          <CardDescription>
            Evolução dos atendimentos nos últimos 6 meses
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {monthlyData.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="font-medium">{item.month}</div>
                <div className="flex space-x-4 text-sm">
                  <span className="text-green-600">Compareceram: {item.completed}</span>
                  <span className="text-red-600">Faltaram: {item.noShow}</span>
                  <span className="text-gray-600">Total: {item.total}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminReports;
