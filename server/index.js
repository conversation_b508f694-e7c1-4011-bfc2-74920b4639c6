import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { Redis } from 'ioredis';
import { auth, authUtils } from './auth.js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.SERVER_PORT || 3001;

// Middleware
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://aptor.datanerd.com.br']
    : ['http://localhost:3001', 'http://localhost:5173'],
  credentials: true,
}));
app.use(express.json());

// Better Auth routes
app.use('/api/auth', auth.handler);

// ESM-friendly path helpers
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Serve static files from public directory (frontend build output)
const publicPath = path.resolve(__dirname, '../public');
const indexPath = path.join(publicPath, 'index.html');
console.log('🗂️  Static dir:', publicPath, 'exists:', fs.existsSync(publicPath));
console.log('📄 index.html exists:', fs.existsSync(indexPath));
app.use(express.static(publicPath));

// Root route -> serve SPA index.html
app.get('/', (_req, res) => {
  res.sendFile(path.join(publicPath, 'index.html'));
});

// API info route
app.get('/api', (_req, res) => {
  res.json({
    name: 'Aptor Redis API',
    version: '1.0.0',
    status: 'running',
    endpoints: {
      health: '/api/health',
      sync: '/api/sync/agendamento-whatsapp',
      metrics: '/api/metrics',
      availability: '/api/availability/:profissionalId/:date',
      lock: '/api/lock/:profissionalId/:date/:time',
      unlock: '/api/unlock/:profissionalId/:date/:time'
    },
    timestamp: new Date().toISOString()
  });
});

// Redis configuration - MANTÉM REDIS CLOUD EXTERNO
const redis = new Redis({
  host: process.env.REDIS_HOST || 'redis-14154.c308.sa-east-1-1.ec2.redns.redis-cloud.com',
  port: parseInt(process.env.REDIS_PORT || '14154'),
  username: process.env.REDIS_USERNAME || 'hordak',
  password: process.env.REDIS_PASSWORD || '3B4q!t#T8xuvPDf',
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: 3,
  connectTimeout: 10000,
  lazyConnect: true,
});

// Redis Service Functions
const redisService = {
  // Health Check
  async healthCheck() {
    try {
      const result = await redis.ping();
      return result === 'PONG';
    } catch (error) {
      console.error('Redis health check failed:', error);
      return false;
    }
  },

  // Availability Cache
  async getAvailability(profissionalId, date) {
    try {
      const key = `disponibilidade:${profissionalId}:${date}`;
      const cached = await redis.get(key);
      return cached ? JSON.parse(cached) : [];
    } catch (error) {
      console.error('Error getting availability from Redis:', error);
      return [];
    }
  },

  async setAvailability(profissionalId, date, slots, ttl = 300) {
    try {
      const key = `disponibilidade:${profissionalId}:${date}`;
      await redis.setex(key, ttl, JSON.stringify(slots));
      return true;
    } catch (error) {
      console.error('Error setting availability in Redis:', error);
      return false;
    }
  },

  async invalidateAvailability(profissionalId, date) {
    try {
      if (date) {
        const key = `disponibilidade:${profissionalId}:${date}`;
        await redis.del(key);
      } else {
        const pattern = `disponibilidade:${profissionalId}:*`;
        const keys = await redis.keys(pattern);
        if (keys.length > 0) {
          await redis.del(...keys);
        }
      }
      return true;
    } catch (error) {
      console.error('Error invalidating availability in Redis:', error);
      return false;
    }
  },

  // Lock System
  async lockTimeSlot(profissionalId, dataHora, sessionId, ttl = 30) {
    try {
      const key = `lock:agendamento:${profissionalId}:${dataHora}`;
      const result = await redis.set(key, sessionId, 'EX', ttl, 'NX');
      return result === 'OK';
    } catch (error) {
      console.error('Error locking time slot:', error);
      return false;
    }
  },

  async unlockTimeSlot(profissionalId, dataHora, sessionId) {
    try {
      const key = `lock:agendamento:${profissionalId}:${dataHora}`;
      const script = `
        if redis.call("GET", KEYS[1]) == ARGV[1] then
          return redis.call("DEL", KEYS[1])
        else
          return 0
        end
      `;
      const result = await redis.eval(script, 1, key, sessionId);
      return result === 1;
    } catch (error) {
      console.error('Error unlocking time slot:', error);
      return false;
    }
  },

  async isTimeSlotLocked(profissionalId, dataHora) {
    try {
      const key = `lock:agendamento:${profissionalId}:${dataHora}`;
      const lockedBy = await redis.get(key);
      return {
        locked: !!lockedBy,
        lockedBy: lockedBy || undefined
      };
    } catch (error) {
      console.error('Error checking time slot lock:', error);
      return { locked: false };
    }
  },

  // Metrics
  async incrementMetric(metric, value = 1) {
    try {
      const key = `metrics:${metric}:${new Date().toISOString().split('T')[0]}`;
      await redis.incrby(key, value);
      await redis.expire(key, 86400 * 7); // Keep for 7 days
      return true;
    } catch (error) {
      console.error('Error incrementing metric:', error);
      return false;
    }
  },

  async getMetric(metric, date) {
    try {
      const dateStr = date || new Date().toISOString().split('T')[0];
      const key = `metrics:${metric}:${dateStr}`;
      const result = await redis.get(key);
      return parseInt(result || '0');
    } catch (error) {
      console.error('Error getting metric:', error);
      return 0;
    }
  }
};

// Generate mock time slots
function generateTimeSlots() {
  const slots = [];
  
  // Morning slots: 8:00 - 12:00
  for (let hour = 8; hour < 12; hour++) {
    slots.push({
      time: `${hour.toString().padStart(2, '0')}:00`,
      available: Math.random() > 0.3, // 70% chance of being available
      locked: false
    });
  }
  
  // Afternoon slots: 14:00 - 18:00
  for (let hour = 14; hour < 18; hour++) {
    slots.push({
      time: `${hour.toString().padStart(2, '0')}:00`,
      available: Math.random() > 0.3, // 70% chance of being available
      locked: false
    });
  }
  
  return slots;
}

// Import auth middleware
import { requireAuth, requireProfissional, requireSuperAdmin, requireAdmin, requireActiveUser, optionalAuth, rateLimit } from './middleware/auth.js';

// API Routes

// Health Check
app.get('/api/health', async (req, res) => {
  try {
    const redisHealth = await redisService.healthCheck();
    res.json({
      status: 'ok',
      redis: redisHealth,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      error: error.message
    });
  }
});

// Get Available Slots
app.get('/api/availability/:profissionalId/:date', async (req, res) => {
  try {
    const { profissionalId, date } = req.params;
    
    // Try cache first
    let slots = await redisService.getAvailability(profissionalId, date);
    
    if (slots.length === 0) {
      // Cache miss - generate mock data
      console.log(`Cache MISS for ${profissionalId} on ${date}`);
      slots = generateTimeSlots();
      
      // Cache the result
      await redisService.setAvailability(profissionalId, date, slots, 300);
      await redisService.incrementMetric('cache_misses');
    } else {
      console.log(`Cache HIT for ${profissionalId} on ${date}`);
      await redisService.incrementMetric('cache_hits');
    }
    
    res.json({
      success: true,
      data: slots,
      cached: slots.length > 0
    });
  } catch (error) {
    console.error('Error getting availability:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Lock Time Slot
app.post('/api/lock/:profissionalId/:date/:time', async (req, res) => {
  try {
    const { profissionalId, date, time } = req.params;
    const { sessionId } = req.body;
    
    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: 'sessionId is required'
      });
    }
    
    const dataHora = `${date}T${time}:00`;
    const locked = await redisService.lockTimeSlot(profissionalId, dataHora, sessionId, 30);
    
    if (locked) {
      await redisService.incrementMetric('slots_locked');
    } else {
      await redisService.incrementMetric('lock_conflicts');
    }
    
    res.json({
      success: true,
      locked,
      sessionId,
      dataHora
    });
  } catch (error) {
    console.error('Error locking time slot:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Unlock Time Slot
app.delete('/api/lock/:profissionalId/:date/:time', async (req, res) => {
  try {
    const { profissionalId, date, time } = req.params;
    const { sessionId } = req.body;
    
    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: 'sessionId is required'
      });
    }
    
    const dataHora = `${date}T${time}:00`;
    const unlocked = await redisService.unlockTimeSlot(profissionalId, dataHora, sessionId);
    
    if (unlocked) {
      await redisService.incrementMetric('slots_unlocked');
    }
    
    res.json({
      success: true,
      unlocked,
      sessionId,
      dataHora
    });
  } catch (error) {
    console.error('Error unlocking time slot:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Invalidate Cache
app.delete('/api/cache/:profissionalId/:date', async (req, res) => {
  try {
    const { profissionalId, date } = req.params;
    
    const invalidated = await redisService.invalidateAvailability(profissionalId, date);
    
    if (invalidated) {
      await redisService.incrementMetric('cache_invalidations');
    }
    
    res.json({
      success: true,
      invalidated,
      profissionalId,
      date: date || 'all'
    });
  } catch (error) {
    console.error('Error invalidating cache:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get Metrics
app.get('/api/metrics/:date', async (req, res) => {
  try {
    const { date } = req.params;

    const [cacheHits, cacheMisses, slotsLocked, lockConflicts, cacheInvalidations, slotsUnlocked] = await Promise.all([
      redisService.getMetric('cache_hits', date),
      redisService.getMetric('cache_misses', date),
      redisService.getMetric('slots_locked', date),
      redisService.getMetric('lock_conflicts', date),
      redisService.getMetric('cache_invalidations', date),
      redisService.getMetric('slots_unlocked', date)
    ]);

    const totalRequests = cacheHits + cacheMisses;
    const cacheHitRate = totalRequests > 0 ? (cacheHits / totalRequests * 100).toFixed(2) : '0.00';

    res.json({
      success: true,
      date: date || new Date().toISOString().split('T')[0],
      metrics: {
        cache: {
          hits: cacheHits,
          misses: cacheMisses,
          hitRate: `${cacheHitRate}%`,
          invalidations: cacheInvalidations
        },
        locks: {
          slotsLocked,
          slotsUnlocked,
          lockConflicts,
          conflictRate: slotsLocked > 0 ? `${(lockConflicts / slotsLocked * 100).toFixed(2)}%` : '0.00%'
        }
      }
    });
  } catch (error) {
    console.error('Error getting metrics:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Invalidate Cache (all dates for professional)
app.delete('/api/cache/:profissionalId', async (req, res) => {
  try {
    const { profissionalId } = req.params;

    const invalidated = await redisService.invalidateAvailability(profissionalId);

    if (invalidated) {
      await redisService.incrementMetric('cache_invalidations');
    }

    res.json({
      success: true,
      invalidated,
      profissionalId,
      date: 'all'
    });
  } catch (error) {
    console.error('Error invalidating cache:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get Metrics (without date)
app.get('/api/metrics', async (req, res) => {
  try {
    const today = new Date().toISOString().split('T')[0];

    const [cacheHits, cacheMisses, slotsLocked, lockConflicts, cacheInvalidations, slotsUnlocked] = await Promise.all([
      redisService.getMetric('cache_hits', today),
      redisService.getMetric('cache_misses', today),
      redisService.getMetric('slots_locked', today),
      redisService.getMetric('lock_conflicts', today),
      redisService.getMetric('cache_invalidations', today),
      redisService.getMetric('slots_unlocked', today)
    ]);

    const totalRequests = cacheHits + cacheMisses;
    const cacheHitRate = totalRequests > 0 ? (cacheHits / totalRequests * 100).toFixed(2) : '0.00';

    res.json({
      success: true,
      date: today,
      metrics: {
        cache: {
          hits: cacheHits,
          misses: cacheMisses,
          hitRate: `${cacheHitRate}%`,
          invalidations: cacheInvalidations
        },
        locks: {
          slotsLocked,
          slotsUnlocked,
          lockConflicts,
          conflictRate: slotsLocked > 0 ? `${(lockConflicts / slotsLocked * 100).toFixed(2)}%` : '0.00%'
        }
      }
    });
  } catch (error) {
    console.error('Error getting metrics:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ===========================================
// ROTAS PROTEGIDAS - ÁREA DO PROFISSIONAL
// ===========================================

// Dashboard do profissional
app.get('/api/profissional/dashboard', requireAuth, requireActiveUser, requireProfissional, async (req, res) => {
  try {
    const { user } = req;
    const today = new Date().toISOString().split('T')[0];

    // Buscar métricas do profissional (exemplo)
    const [appointments, metrics] = await Promise.all([
      // Aqui você buscaria os agendamentos do profissional
      Promise.resolve([]), // Placeholder
      redisService.getMetric('whatsapp_appointments', today)
    ]);

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
        },
        dashboard: {
          appointments_today: appointments.length,
          total_appointments: metrics || 0,
          last_sync: new Date().toISOString(),
        }
      }
    });
  } catch (error) {
    console.error('❌ Dashboard error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Erro ao carregar dashboard'
    });
  }
});

// Perfil do profissional
app.get('/api/profissional/profile', requireAuth, requireActiveUser, requireProfissional, async (req, res) => {
  try {
    const { user } = req;

    res.json({
      success: true,
      data: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        phone: user.phone || null,
        avatar: user.avatar || null,
        created_at: user.created_at || null,
        last_login: user.lastLogin || null,
      }
    });
  } catch (error) {
    console.error('❌ Profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Erro ao carregar perfil'
    });
  }
});

// Logout (adicionar token à blacklist)
app.post('/api/auth/logout', requireAuth, async (req, res) => {
  try {
    const { token, user } = req;

    // Adicionar token à blacklist
    await authUtils.blacklistToken(token);

    // Limpar cache do usuário
    await authUtils.clearUserCache(user.id);

    console.log(`🚪 User logged out: ${user.email}`);

    res.json({
      success: true,
      message: 'Logout realizado com sucesso'
    });
  } catch (error) {
    console.error('❌ Logout error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Erro ao fazer logout'
    });
  }
});

// ===========================================
// ROTAS DE SUPERADMIN - GERENCIAMENTO DE USUÁRIOS
// ===========================================

// Listar todos os usuários (apenas superadmin)
app.get('/api/superadmin/users', requireAuth, requireSuperAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 20, search = '', role = '' } = req.query;

    // Simular busca de usuários (implementar com Supabase depois)
    const mockUsers = [
      {
        id: '1',
        name: 'João Silva',
        email: '<EMAIL>',
        role: 'profissional',
        is_active: true,
        created_at: '2025-01-15T10:00:00Z',
        last_login: '2025-08-23T08:30:00Z',
      },
      {
        id: '2',
        name: 'Maria Santos',
        email: '<EMAIL>',
        role: 'profissional',
        is_active: false,
        created_at: '2025-02-10T14:20:00Z',
        last_login: '2025-08-20T16:45:00Z',
      }
    ];

    res.json({
      success: true,
      data: {
        users: mockUsers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: mockUsers.length,
          pages: Math.ceil(mockUsers.length / limit)
        }
      }
    });
  } catch (error) {
    console.error('❌ List users error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Erro ao listar usuários'
    });
  }
});

// Criar novo usuário (apenas superadmin)
app.post('/api/superadmin/users', requireAuth, requireSuperAdmin, async (req, res) => {
  try {
    const { name, email, role = 'profissional', password } = req.body;

    if (!name || !email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        message: 'Nome, email e senha são obrigatórios'
      });
    }

    // Validar role
    const validRoles = ['profissional', 'admin', 'superadmin'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid role',
        message: 'Role inválida'
      });
    }

    // TODO: Implementar criação no Supabase
    const newUser = {
      id: `user_${Date.now()}`,
      name,
      email,
      role,
      is_active: true,
      created_at: new Date().toISOString(),
      created_by: req.user.id
    };

    console.log(`👤 SuperAdmin ${req.user.email} created user: ${email} with role: ${role}`);

    res.status(201).json({
      success: true,
      message: 'Usuário criado com sucesso',
      data: newUser
    });
  } catch (error) {
    console.error('❌ Create user error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Erro ao criar usuário'
    });
  }
});

// Atualizar usuário (apenas superadmin)
app.put('/api/superadmin/users/:userId', requireAuth, requireSuperAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { name, email, role, is_active } = req.body;

    // Validar se não está tentando desativar a si mesmo
    if (userId === req.user.id && is_active === false) {
      return res.status(400).json({
        success: false,
        error: 'Cannot deactivate self',
        message: 'Você não pode desativar sua própria conta'
      });
    }

    // TODO: Implementar atualização no Supabase
    const updatedUser = {
      id: userId,
      name,
      email,
      role,
      is_active,
      updated_at: new Date().toISOString(),
      updated_by: req.user.id
    };

    // Log da ação
    const action = is_active === false ? 'deactivated' : 'updated';
    console.log(`👤 SuperAdmin ${req.user.email} ${action} user: ${email}`);

    // Invalidar cache do usuário se foi desativado
    if (is_active === false) {
      await authUtils.clearUserCache(userId);
      // TODO: Adicionar todos os tokens do usuário à blacklist
    }

    res.json({
      success: true,
      message: `Usuário ${action === 'deactivated' ? 'desativado' : 'atualizado'} com sucesso`,
      data: updatedUser
    });
  } catch (error) {
    console.error('❌ Update user error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Erro ao atualizar usuário'
    });
  }
});

// Deletar usuário (apenas superadmin)
app.delete('/api/superadmin/users/:userId', requireAuth, requireSuperAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    // Validar se não está tentando deletar a si mesmo
    if (userId === req.user.id) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete self',
        message: 'Você não pode deletar sua própria conta'
      });
    }

    // TODO: Implementar deleção no Supabase (soft delete recomendado)
    console.log(`👤 SuperAdmin ${req.user.email} deleted user: ${userId}`);

    // Limpar cache e invalidar tokens
    await authUtils.clearUserCache(userId);

    res.json({
      success: true,
      message: 'Usuário deletado com sucesso'
    });
  } catch (error) {
    console.error('❌ Delete user error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Erro ao deletar usuário'
    });
  }
});

// Estatísticas do sistema (superadmin)
app.get('/api/superadmin/stats', requireAuth, requireSuperAdmin, async (req, res) => {
  try {
    // TODO: Implementar estatísticas reais
    const stats = {
      total_users: 25,
      active_users: 22,
      inactive_users: 3,
      total_appointments: 1250,
      appointments_today: 15,
      system_health: 'healthy',
      redis_status: 'connected',
      last_backup: '2025-08-23T02:00:00Z'
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('❌ Stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Erro ao carregar estatísticas'
    });
  }
});

// ===========================================
// ROTAS PÚBLICAS (N8N, HEALTH, ETC.)
// ===========================================

// N8N Sync Endpoint - GET para teste
app.get('/api/sync/agendamento-whatsapp', (req, res) => {
  res.json({
    message: 'Aptor N8N Sync Endpoint',
    method: 'POST required',
    description: 'Use POST method to sync WhatsApp appointments',
    example: {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: {
        profissional_id: 'prof-123',
        cliente_nome: 'João Silva',
        cliente_telefone: '11999999999',
        data_hora: '2025-08-21T14:00:00.000Z',
        source: 'whatsapp'
      }
    },
    timestamp: new Date().toISOString()
  });
});

// N8N Sync Endpoint - POST para produção
app.post('/api/sync/agendamento-whatsapp', async (req, res) => {
  try {
    const data = req.body;

    // Validate required fields
    if (!data.profissional_id || !data.cliente_nome || !data.cliente_telefone || !data.data_hora) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        message: 'profissional_id, cliente_nome, cliente_telefone, and data_hora are required'
      });
    }

    console.log(`📱 Received WhatsApp appointment sync:`, {
      profissional_id: data.profissional_id,
      cliente_nome: data.cliente_nome,
      data_hora: data.data_hora
    });

    // Extract date from data_hora for cache invalidation
    const appointmentDate = new Date(data.data_hora);
    const dateStr = appointmentDate.toISOString().split('T')[0]; // YYYY-MM-DD

    // 🗑️ Invalidate availability cache for this professional and date
    await redisService.invalidateAvailability(data.profissional_id, dateStr);
    console.log(`🗑️ Invalidated cache for ${data.profissional_id} on ${dateStr}`);

    // 📊 Update metrics
    await redisService.incrementMetric('whatsapp_appointments');
    await redisService.incrementMetric('cache_invalidations');

    // Success response
    res.json({
      success: true,
      message: 'WhatsApp appointment synchronized successfully',
      data: {
        profissional_id: data.profissional_id,
        date: dateStr,
        cache_invalidated: true,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Error syncing WhatsApp appointment:', error);

    // Increment error metric
    await redisService.incrementMetric('sync_errors');

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to sync WhatsApp appointment'
    });
  }
});

// Catch-all for SPA (must be after ALL API routes and static)
app.use((req, res, next) => {
  if (req.path.startsWith('/api/')) return next();
  res.sendFile(path.join(publicPath, 'index.html'));
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Aptor Complete App running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔄 N8N Sync: http://localhost:${PORT}/api/sync/agendamento-whatsapp`);
  console.log(`🎨 Frontend: http://localhost:${PORT}/`);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 Shutting down server...');
  await redis.disconnect();
  process.exit(0);
});
