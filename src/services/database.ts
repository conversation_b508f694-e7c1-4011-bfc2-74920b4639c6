import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

// Use types from the Supabase generated types
export type Cliente = Database['public']['Tables']['clientes']['Row'];
export type CreateClienteData = Database['public']['Tables']['clientes']['Insert'];
export type UpdateClienteData = Database['public']['Tables']['clientes']['Update'];

// Use types from the Supabase generated types for all tables
export type Profissional = Database['public']['Tables']['profissionais']['Row'];
export type CreateProfissionalData = Database['public']['Tables']['profissionais']['Insert'];
export type UpdateProfissionalData = Database['public']['Tables']['profissionais']['Update'];

export type HorarioTrabalho = Database['public']['Tables']['horarios_trabalho']['Row'];
export type CreateHorarioTrabalhoData = Database['public']['Tables']['horarios_trabalho']['Insert'];
export type UpdateHorarioTrabalhoData = Database['public']['Tables']['horarios_trabalho']['Update'];

export type Agendamento = Database['public']['Tables']['agendamentos']['Row'];
export type CreateAgendamentoData = Database['public']['Tables']['agendamentos']['Insert'];
export type UpdateAgendamentoData = Database['public']['Tables']['agendamentos']['Update'];

export type WebhookLog = Database['public']['Tables']['webhooks_log']['Row'];
export type CreateWebhookLogData = Database['public']['Tables']['webhooks_log']['Insert'];

// Serviços para Clientes
export const clienteService = {
  async getAll(): Promise<Cliente[]> {
    const { data, error } = await supabase
      .from('clientes')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  },

  async getById(id: string): Promise<Cliente | null> {
    const { data, error } = await supabase
      .from('clientes')
      .select('*')
      .eq('id', id)
      .maybeSingle();
    
    if (error) throw error;
    return data;
  },

  async create(cliente: CreateClienteData): Promise<Cliente> {
    const { data, error } = await supabase
      .from('clientes')
      .insert(cliente)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async update(id: string, updates: UpdateClienteData): Promise<Cliente> {
    const { data, error } = await supabase
      .from('clientes')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('clientes')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  async searchByWhatsApp(numero: string): Promise<Cliente | null> {
    const { data, error } = await supabase
      .from('clientes')
      .select('*')
      .eq('numero_whatsapp', numero)
      .maybeSingle();
    
    if (error) throw error;
    return data;
  }
};

// Serviços para Profissionais
export const profissionalService = {
  async getAll(): Promise<Profissional[]> {
    const { data, error } = await supabase
      .from('profissionais')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  },

  async getById(id: string): Promise<Profissional | null> {
    const { data, error } = await supabase
      .from('profissionais')
      .select('*')
      .eq('id', id)
      .maybeSingle();
    
    if (error) throw error;
    return data;
  },

  async create(profissional: CreateProfissionalData): Promise<Profissional> {
    const { data, error } = await supabase
      .from('profissionais')
      .insert(profissional)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async update(id: string, updates: UpdateProfissionalData): Promise<Profissional> {
    const { data, error } = await supabase
      .from('profissionais')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('profissionais')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
};

// Serviços para Horários de Trabalho
export const horarioTrabalhoService = {
  async getAll(): Promise<HorarioTrabalho[]> {
    const { data, error } = await supabase
      .from('horarios_trabalho')
      .select('*')
      .order('dia_semana', { ascending: true });
    
    if (error) throw error;
    return data || [];
  },

  async getByProfissional(profissionalId: string): Promise<HorarioTrabalho[]> {
    const { data, error } = await supabase
      .from('horarios_trabalho')
      .select('*')
      .eq('profissional_id', profissionalId)
      .eq('ativo', true)
      .order('dia_semana', { ascending: true });
    
    if (error) throw error;
    return data || [];
  },

  async create(horario: CreateHorarioTrabalhoData): Promise<HorarioTrabalho> {
    const { data, error } = await supabase
      .from('horarios_trabalho')
      .insert(horario)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async update(id: string, updates: UpdateHorarioTrabalhoData): Promise<HorarioTrabalho> {
    const { data, error } = await supabase
      .from('horarios_trabalho')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('horarios_trabalho')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
};

// Serviços para Agendamentos
export const agendamentoService = {
  async getAll(): Promise<Agendamento[]> {
    const { data, error } = await supabase
      .from('agendamentos')
      .select('*')
      .order('data_hora', { ascending: true });
    
    if (error) throw error;
    return data || [];
  },

  async getById(id: string): Promise<Agendamento | null> {
    const { data, error } = await supabase
      .from('agendamentos')
      .select('*')
      .eq('id', id)
      .maybeSingle();
    
    if (error) throw error;
    return data;
  },

  async getByCliente(clienteId: string): Promise<Agendamento[]> {
    const { data, error } = await supabase
      .from('agendamentos')
      .select('*')
      .eq('cliente_id', clienteId)
      .order('data_hora', { ascending: false });
    
    if (error) throw error;
    return data || [];
  },

  async getByProfissional(profissionalId: string): Promise<Agendamento[]> {
    const { data, error } = await supabase
      .from('agendamentos')
      .select('*')
      .eq('profissional_id', profissionalId)
      .order('data_hora', { ascending: true });
    
    if (error) throw error;
    return data || [];
  },

  async create(agendamento: CreateAgendamentoData): Promise<Agendamento> {
    const { data, error } = await supabase
      .from('agendamentos')
      .insert(agendamento)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async update(id: string, updates: UpdateAgendamentoData): Promise<Agendamento> {
    const { data, error } = await supabase
      .from('agendamentos')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('agendamentos')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
};

// Serviços para Webhooks Log
export const webhookLogService = {
  async getAll(): Promise<WebhookLog[]> {
    const { data, error } = await supabase
      .from('webhooks_log')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  },

  async getByAgendamento(agendamentoId: string): Promise<WebhookLog[]> {
    const { data, error } = await supabase
      .from('webhooks_log')
      .select('*')
      .eq('agendamento_id', agendamentoId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  },

  async create(log: CreateWebhookLogData): Promise<WebhookLog> {
    const { data, error } = await supabase
      .from('webhooks_log')
      .insert(log)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }
};