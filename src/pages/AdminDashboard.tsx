import { CalendarDays, Users, UserX, Clock, Plus, Calendar, TrendingUp, AlertTriangle, CheckCircle, XCircle, RefreshCw, BarChart3, FileText } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { agendamentoService, clienteService, profissionalService } from "@/services/database";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import emailService from "@/services/emailService";
import ProfessionalAnalytics from "@/components/analytics/ProfessionalAnalytics";
import InteractiveReminders from "@/components/dashboard/InteractiveReminders";
import ReminderDetailsModal from "@/components/modals/ReminderDetailsModal";

const AdminDashboard = () => {
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [reminderModalOpen, setReminderModalOpen] = useState(false);
  const [reminderModalType, setReminderModalType] = useState('');
  const [reminderModalData, setReminderModalData] = useState([]);
  const { toast } = useToast();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  
  // Fetch today's appointments
  const { data: appointments = [] } = useQuery({
    queryKey: ['appointments'],
    queryFn: agendamentoService.getAll
  });

  // Fetch clients and professionals for joins
  const { data: clients = [] } = useQuery({
    queryKey: ['clients'],
    queryFn: clienteService.getAll
  });

  const { data: professionals = [] } = useQuery({
    queryKey: ['professionals'],
    queryFn: profissionalService.getAll
  });

  // Mutations for updating appointment and client status
  const updateAppointmentMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      agendamentoService.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      toast({
        title: "Status atualizado!",
        description: "O status do atendimento foi atualizado com sucesso.",
      });
    },
    onError: (error) => {
      console.error("Erro ao atualizar atendimento:", error);
      toast({
        title: "Erro ao atualizar",
        description: "Ocorreu um erro ao atualizar o status. Tente novamente.",
        variant: "destructive"
      });
    }
  });

  const updateClientMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      clienteService.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clients'] });
    },
    onError: (error) => {
      console.error("Erro ao atualizar cliente:", error);
    }
  });

  // Filter today's appointments
  const today = new Date().toISOString().split('T')[0];
  const now = new Date();
  
  const todayAppointments = appointments
    .filter(apt => apt.data_hora.startsWith(today))
    .map(apt => {
      const client = clients.find(c => c.id === apt.cliente_id);
      const professional = professionals.find(p => p.id === apt.profissional_id);
      const appointmentTime = new Date(apt.data_hora);
      const minutesDiff = Math.floor((now.getTime() - appointmentTime.getTime()) / (1000 * 60));

      // Determine real-time status based on time and current status
      let realTimeStatus = apt.status || 'agendado';
      let statusLabel = '';
      let statusColor = '';

      // Only apply time-based status if not already confirmed/completed
      if (!['confirmado', 'concluído', 'cancelado'].includes(apt.status || '')) {
        if (minutesDiff > 15) {
          realTimeStatus = 'no_show';
          statusLabel = 'Não veio';
          statusColor = 'bg-red-100 text-red-800 border-red-200';
        } else if (minutesDiff > 3) {
          realTimeStatus = 'late';
          statusLabel = 'Atrasado';
          statusColor = 'bg-orange-100 text-orange-800 border-orange-200';
        } else if (minutesDiff > -5) {
          statusLabel = 'Agora';
          statusColor = 'bg-blue-100 text-blue-800 border-blue-200';
        } else {
          statusLabel = 'Agendado';
          statusColor = 'bg-gray-100 text-gray-800 border-gray-200';
        }
      } else {
        // Use existing status for confirmed/completed appointments
        switch (apt.status) {
          case 'confirmado':
            statusLabel = 'Confirmado';
            statusColor = 'bg-green-100 text-green-800 border-green-200';
            break;
          case 'concluído':
            statusLabel = 'Concluído';
            statusColor = 'bg-green-100 text-green-800 border-green-200';
            break;
          case 'ausente':
            statusLabel = 'Ausente';
            statusColor = 'bg-orange-100 text-orange-800 border-orange-200';
            break;
          case 'cancelado':
            statusLabel = 'Cancelado';
            statusColor = 'bg-red-100 text-red-800 border-red-200';
            break;
          default:
            statusLabel = 'Agendado';
            statusColor = 'bg-gray-100 text-gray-800 border-gray-200';
        }
      }

      return {
        ...apt,
        patient: client?.nome || 'Cliente não encontrado',
        clientData: client,
        time: appointmentTime.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
        type: 'Atendimento',
        realTimeStatus,
        statusLabel,
        statusColor,
        minutesDiff,
        canConfirm: !['confirmado', 'concluído', 'ausente', 'cancelado'].includes(apt.status || ''),
        canMarkNoShow: !['concluído', 'ausente', 'cancelado'].includes(apt.status || '')
      };
    })
    .sort((a, b) => new Date(a.data_hora).getTime() - new Date(b.data_hora).getTime());

  // Calculate cancellations/no-shows for the week
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
  const weeklyAppointments = appointments.filter(apt => {
    const aptDate = new Date(apt.data_hora);
    return aptDate >= oneWeekAgo && (
      apt.status === 'cancelado' ||
      apt.status === 'ausente' ||
      apt.status === 'no_show'
    );
  });

  // Calculate reminder data
  const weeklyNoShows = appointments.filter(apt => {
    const aptDate = new Date(apt.data_hora);
    return aptDate >= oneWeekAgo && (apt.status === 'no_show' || apt.status === 'ausente');
  });

  const clientsWithoutConfirmation = clients.filter(client => {
    const hasRecentAppointments = appointments.some(apt => {
      const aptDate = new Date(apt.data_hora);
      return apt.cliente_id === client.id &&
             aptDate >= oneWeekAgo &&
             !['confirmado', 'concluído', 'cancelado'].includes(apt.status || '');
    });
    return hasRecentAppointments;
  });

  const inactiveClients = clients.filter(client => {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const lastAppointment = appointments
      .filter(apt => apt.cliente_id === client.id)
      .sort((a, b) => new Date(b.data_hora).getTime() - new Date(a.data_hora).getTime())[0];

    return !lastAppointment || new Date(lastAppointment.data_hora) < thirtyDaysAgo;
  });

  const stats = {
    todayAppointments: todayAppointments.length,
    weeklyPatients: clients.length, // Simplified for now
    weeklyCancellations: weeklyAppointments.length,
    nextAppointment: todayAppointments[0]?.time || "Nenhum"
  };

  // Action handlers
  const handleConfirmPresence = async (appointment: any) => {
    try {
      // Update appointment status
      await updateAppointmentMutation.mutateAsync({
        id: appointment.id,
        data: { status: 'confirmado' }
      });

      // Update client presence status
      if (appointment.clientData) {
        await updateClientMutation.mutateAsync({
          id: appointment.clientData.id,
          data: { presenca_status: 'show' }
        });

        // Send confirmation email to client if they have email
        if (appointment.clientData.email && professionals.length > 0) {
          try {
            const professional = professionals[0];
            const { date, time } = emailService.formatAppointmentDateTime(appointment.data_hora);

            await emailService.sendConfirmationEmail({
              clientEmail: appointment.clientData.email,
              clientName: appointment.clientData.nome,
              professionalName: professional.nome,
              professionalEmail: professional.email,
              professionalPhone: professional.telefone,
              appointmentDate: date,
              appointmentTime: time,
              appointmentDuration: professional.duracao_consulta || 30,
              appointmentNotes: appointment.observacoes
            });

            console.log('Confirmation email sent for confirmed appointment');
          } catch (emailError) {
            console.error('Error sending confirmation email:', emailError);
          }
        }
      }

      // Send webhook notification to N8N
      try {
        const { notifyAgendamentoAtualizado } = await import('../services/webhooks');
        const professional = professionals.find(p => p.id === appointment.profissional_id);
        await notifyAgendamentoAtualizado(
          { ...appointment, status: 'confirmado' },
          appointment.clientData,
          professional
        );
      } catch (webhookError) {
        console.warn("Erro ao enviar webhook:", webhookError);
        // Don't fail the entire process if webhook fails
      }

      toast({
        title: "Presença confirmada!",
        description: "O cliente foi marcado como presente.",
      });
    } catch (error) {
      console.error("Erro ao confirmar presença:", error);
    }
  };

  const handleMarkNoShow = async (appointment: any) => {
    try {
      // Update appointment status to 'ausente' (consistent with auto-presence-check function)
      await updateAppointmentMutation.mutateAsync({
        id: appointment.id,
        data: { status: 'ausente' }
      });

      // Update client presence status to 'no_show'
      if (appointment.clientData) {
        await updateClientMutation.mutateAsync({
          id: appointment.clientData.id,
          data: { presenca_status: 'no_show' }
        });
      }

      // Send webhook notification to N8N
      try {
        const { notifyAgendamentoAtualizado } = await import('../services/webhooks');
        const professional = professionals.find(p => p.id === appointment.profissional_id);
        await notifyAgendamentoAtualizado(
          { ...appointment, status: 'ausente' },
          appointment.clientData,
          professional
        );
      } catch (webhookError) {
        console.warn("Erro ao enviar webhook:", webhookError);
        // Don't fail the entire process if webhook fails
      }

      toast({
        title: "Cliente marcado como ausente",
        description: "O status foi atualizado com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao marcar ausência:", error);
      toast({
        title: "Erro ao marcar ausência",
        description: "Ocorreu um erro ao atualizar o status. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  // Reminder handlers
  const handleViewReminderDetails = (type: string, data: any[]) => {
    setReminderModalType(type);
    setReminderModalData(data);
    setReminderModalOpen(true);
  };

  const closeReminderModal = () => {
    setReminderModalOpen(false);
    setReminderModalType('');
    setReminderModalData([]);
  };

  return (
    <div className="min-h-screen bg-gradient-secondary">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 animate-fade-in-up">
          <div>
            <h1 className="text-3xl font-bold text-primary mb-2">Dashboard</h1>
            <p className="text-muted-foreground">
              Bem-vindo, {professionals[0]?.nome || 'Profissional'} - {new Date().toLocaleDateString('pt-BR', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
          <div className="flex flex-wrap gap-3 mt-4 md:mt-0">
            <Button 
              variant={showAnalytics ? "outline" : "default"}
              onClick={() => setShowAnalytics(!showAnalytics)}
              className={!showAnalytics ? "bg-gradient-primary shadow-elegant" : ""}
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              {showAnalytics ? "Dashboard" : "Analytics"}
            </Button>
            <Button className={showAnalytics ? "bg-gradient-primary shadow-elegant" : ""} variant={showAnalytics ? "default" : "outline"}>
              <Plus className="h-4 w-4 mr-2" />
              Novo Atendimento
            </Button>
            <Button variant="outline" className="hidden sm:flex">
              <Calendar className="h-4 w-4 mr-2" />
              Ver Calendário
            </Button>
          </div>
        </div>

        {/* Conditional Content */}
        {showAnalytics ? (
          <ProfessionalAnalytics />
        ) : (
          <>
            {/* Stats Cards */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Atendimentos Hoje</CardTitle>
              <CalendarDays className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">{stats.todayAppointments}</div>
              <p className="text-xs text-muted-foreground">
                {stats.nextAppointment !== "Nenhum" ? `Próximo às ${stats.nextAppointment}` : "Nenhum atendimento hoje"}
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Clientes (Semana)</CardTitle>
              <Users className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">{stats.weeklyPatients}</div>
              <p className="text-xs text-muted-foreground">
                Total de clientes cadastrados
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Cancelamentos (Semana)</CardTitle>
              <UserX className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">
                {stats.weeklyCancellations}
              </div>
              <p className="text-xs text-muted-foreground">
                Cancelamentos e faltas
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-card border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium flex items-center">
                <TrendingUp className="h-4 w-4 mr-2 text-primary" />
                Próximo Atendimento
              </CardTitle>
              <Clock className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-primary">{stats.nextAppointment}</div>
              <p className="text-sm text-muted-foreground font-medium">
                {todayAppointments[0]?.patient || "Nenhum atendimento hoje"}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Today's Appointments */}
        <div className="grid gap-8 lg:grid-cols-3">
          <div className="lg:col-span-2">
            <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.5s' }}>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5 text-primary" />
                  <span>Atendimentos de Hoje</span>
                </CardTitle>
                <CardDescription>
                  {todayAppointments.length} atendimentos agendados para hoje
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {todayAppointments.length > 0 ? todayAppointments.map((appointment, index) => (
                    <div
                      key={appointment.id}
                      className="flex items-center justify-between p-4 bg-gradient-card rounded-lg border animate-fade-in-up"
                      style={{ animationDelay: `${0.6 + index * 0.1}s` }}
                    >
                      <div className="flex items-center space-x-4">
                        <Avatar>
                          <AvatarImage src="" />
                          <AvatarFallback className="bg-primary text-primary-foreground">
                            {appointment.patient.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{appointment.patient}</div>
                          <div className="text-sm text-muted-foreground">{appointment.type}</div>
                          {appointment.observacoes && (
                            <div className="text-xs text-muted-foreground mt-1 max-w-xs truncate">
                              {appointment.observacoes}
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        {/* Action buttons */}
                        {(appointment.canConfirm || appointment.canMarkNoShow) && (
                          <div className="flex space-x-1">
                            {appointment.canConfirm && (
                              <Button
                                size="sm"
                                variant="outline"
                                className="h-8 px-2 text-green-700 border-green-300 hover:bg-green-50"
                                onClick={() => handleConfirmPresence(appointment)}
                                disabled={updateAppointmentMutation.isPending}
                                title="Confirmar presença"
                              >
                                <CheckCircle className="h-3 w-3 mr-1" />
                                <span className="hidden sm:inline">Confirmar</span>
                              </Button>
                            )}

                            {appointment.canMarkNoShow && (
                              <Button
                                size="sm"
                                variant="outline"
                                className="h-8 px-2 text-red-700 border-red-300 hover:bg-red-50"
                                onClick={() => handleMarkNoShow(appointment)}
                                disabled={updateAppointmentMutation.isPending}
                                title="Marcar como ausente"
                              >
                                <XCircle className="h-3 w-3 mr-1" />
                                <span className="hidden sm:inline">Ausente</span>
                              </Button>
                            )}
                          </div>
                        )}

                        <div className="flex flex-col items-end space-y-1">
                          <Badge className={appointment.statusColor} variant="outline">
                            {appointment.statusLabel}
                          </Badge>
                          {appointment.minutesDiff > 3 && appointment.canConfirm && (
                            <span className="text-xs text-muted-foreground">
                              {appointment.minutesDiff > 15 ? 'Há mais de 15 min' : `Há ${appointment.minutesDiff} min`}
                            </span>
                          )}
                        </div>

                        <div className="text-lg font-semibold text-primary">
                          {appointment.time}
                        </div>
                      </div>
                    </div>
                  )) : (
                    <div className="text-center py-8 text-muted-foreground">
                      Nenhum atendimento agendado para hoje
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="space-y-6">
            <Card className="shadow-card animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
              <CardHeader>
                <CardTitle>Ações Rápidas</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start" variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Novo Cliente
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Calendar className="h-4 w-4 mr-2" />
                  Bloquear Horário
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Sincronizar Agenda
                </Button>
                <Button
                  className="w-full justify-start"
                  variant="outline"
                  onClick={() => navigate('/admin/patients')}
                >
                  <Users className="h-4 w-4 mr-2" />
                  Listar Clientes
                </Button>
                <Button
                  className="w-full justify-start"
                  variant="outline"
                  onClick={() => navigate('/admin/reports')}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Relatório de Clientes
                </Button>
              </CardContent>
            </Card>

            <div className="animate-fade-in-up" style={{ animationDelay: '0.7s' }}>
              <InteractiveReminders
                data={{
                  weeklyNoShows,
                  clientsWithoutConfirmation,
                  inactiveClients
                }}
                onViewDetails={handleViewReminderDetails}
              />
            </div>
          </div>
            </div>
          </>
        )}

        {/* Reminder Details Modal */}
        <ReminderDetailsModal
          isOpen={reminderModalOpen}
          onClose={closeReminderModal}
          type={reminderModalType}
          data={reminderModalData}
        />
      </div>
    </div>
  );
};

export default AdminDashboard;