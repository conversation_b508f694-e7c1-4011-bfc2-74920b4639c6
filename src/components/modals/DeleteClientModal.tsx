import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { clienteService, type Cliente } from "@/services/database";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AlertTriangle } from "lucide-react";

interface DeleteClientModalProps {
  client: Cliente | null;
  isOpen: boolean;
  onClose: () => void;
}

const DeleteClientModal = ({ client, isOpen, onClose }: DeleteClientModalProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const deleteClientMutation = useMutation({
    mutationFn: (id: string) => clienteService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clients'] });
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      toast({
        title: "Cliente excluído!",
        description: "O cliente foi removido com sucesso.",
      });
      onClose();
    },
    onError: (error) => {
      console.error("Erro ao excluir cliente:", error);
      toast({
        title: "Erro ao excluir",
        description: "Ocorreu um erro ao excluir o cliente. Tente novamente.",
        variant: "destructive"
      });
    }
  });

  const handleDelete = () => {
    if (!client) return;
    deleteClientMutation.mutate(client.id);
  };

  const handleClose = () => {
    if (!deleteClientMutation.isPending) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <span>Confirmar Exclusão</span>
          </DialogTitle>
          <DialogDescription>
            Esta ação não pode ser desfeita. Isso excluirá permanentemente o cliente e todos os seus agendamentos.
          </DialogDescription>
        </DialogHeader>
        
        {client && (
          <div className="py-4">
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">Cliente a ser excluído:</h4>
              <div className="space-y-1 text-sm">
                <div><strong>Nome:</strong> {client.nome}</div>
                <div><strong>WhatsApp:</strong> {client.numero_whatsapp}</div>
                {client.email && <div><strong>E-mail:</strong> {client.email}</div>}
              </div>
            </div>
          </div>
        )}
        
        <DialogFooter>
          <Button 
            type="button" 
            variant="outline" 
            onClick={handleClose}
            disabled={deleteClientMutation.isPending}
          >
            Cancelar
          </Button>
          <Button 
            type="button" 
            variant="destructive"
            onClick={handleDelete}
            disabled={deleteClientMutation.isPending}
          >
            {deleteClientMutation.isPending ? "Excluindo..." : "Excluir Cliente"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteClientModal;
