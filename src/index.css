@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Professional Theme */
    --background: 0 0% 100%;
    --foreground: 210 11% 15%;

    --card: 0 0% 100%;
    --card-foreground: 210 11% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 11% 15%;

    /* Professional Blue Primary */
    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 210 100% 85%;
    --primary-dark: 210 100% 35%;

    /* Soft Professional Background */
    --secondary: 210 50% 97%;
    --secondary-foreground: 210 11% 15%;

    --muted: 210 30% 96%;
    --muted-foreground: 210 10% 45%;

    --accent: 195 100% 50%;
    --accent-foreground: 0 0% 100%;

    /* Professional Alert Colors */
    --destructive: 0 85% 55%;
    --destructive-foreground: 0 0% 100%;
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;

    --border: 210 20% 90%;
    --input: 210 20% 95%;
    --ring: 210 100% 50%;

    --radius: 0.75rem;

    /* Professional Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary)), hsl(var(--muted)));
    --gradient-card: linear-gradient(145deg, hsl(var(--card)), hsl(var(--secondary)));

    /* Professional Shadows */
    --shadow-card: 0 4px 20px -2px hsl(var(--primary) / 0.1);
    --shadow-elegant: 0 8px 30px -8px hsl(var(--primary) / 0.15);
    --shadow-glow: 0 0 40px hsl(var(--primary) / 0.2);

    /* Smooth Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark Professional Theme */
    --background: 210 15% 8%;
    --foreground: 210 20% 92%;

    --card: 210 15% 12%;
    --card-foreground: 210 20% 92%;

    --popover: 210 15% 12%;
    --popover-foreground: 210 20% 92%;

    --primary: 210 100% 60%;
    --primary-foreground: 210 15% 8%;
    --primary-light: 210 100% 75%;
    --primary-dark: 210 100% 45%;

    --secondary: 210 15% 15%;
    --secondary-foreground: 210 20% 92%;

    --muted: 210 15% 18%;
    --muted-foreground: 210 10% 60%;

    --accent: 195 100% 60%;
    --accent-foreground: 210 15% 8%;

    --destructive: 0 85% 60%;
    --destructive-foreground: 0 0% 100%;
    --success: 142 76% 45%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 60%;
    --warning-foreground: 0 0% 100%;

    --border: 210 15% 20%;
    --input: 210 15% 18%;
    --ring: 210 100% 60%;

    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary)), hsl(var(--muted)));
    --gradient-card: linear-gradient(145deg, hsl(var(--card)), hsl(var(--secondary)));

    --shadow-card: 0 4px 20px -2px hsl(0 0% 0% / 0.3);
    --shadow-elegant: 0 8px 30px -8px hsl(0 0% 0% / 0.4);
    --shadow-glow: 0 0 40px hsl(var(--primary) / 0.3);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }

  /* Professional Typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  /* Smooth Animations */
  * {
    transition: var(--transition-smooth);
  }

  /* Custom Scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary/50;
  }
}

/* Professional animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.2);
  }
  50% {
    box-shadow: 0 0 40px hsl(var(--primary) / 0.4);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}