#!/bin/bash

# ===========================================
# APTOR - BUILD PARA UBUNTU (PRODUÇÃO)
# Para deploy em servidor Ubuntu
# ===========================================

set -e

# Verificar se foi passado um nome para a imagem
if [ -z "$1" ]; then
    echo "❌ Erro: Nome da imagem não fornecido"
    echo "📋 Uso: ./docker-build-ubuntu.sh <nome-da-imagem>"
    echo "📋 Exemplo: ./docker-build-ubuntu.sh aptor-complete-v2.2.0"
    exit 1
fi

IMAGE_NAME="datanerd/automation:$1"
PLATFORM="linux/amd64"

echo "🚀 Iniciando build para Ubuntu (Produção)..."
echo "📦 Imagem: $IMAGE_NAME"
echo "🏗️  Plataforma: $PLATFORM"
echo "💻 Host: $(uname -s) $(uname -m)"

# Verificar se Docker está rodando
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker não está rodando. Inicie o Docker Desktop."
    exit 1
fi

# Build da imagem
echo "🔨 Construindo imagem..."
docker build \
    --platform $PLATFORM \
    -t "$IMAGE_NAME" \
    -f Dockerfile \
    .

if [ $? -eq 0 ]; then
    echo "✅ Build concluído com sucesso!"
    echo "📦 Imagem: $IMAGE_NAME"
    echo "🏗️  Plataforma: $PLATFORM"
    
    # Mostrar detalhes da imagem
    echo ""
    echo "📊 Detalhes da imagem:"
    docker images "$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    # Push para Docker Hub
    echo ""
    echo "🚀 Enviando para Docker Hub..."
    docker push "$IMAGE_NAME"
    
    if [ $? -eq 0 ]; then
        echo "✅ Push concluído com sucesso!"
        echo "🌐 Disponível em: docker pull $IMAGE_NAME"
        echo ""
        echo "🎯 Para deploy no servidor Ubuntu:"
        echo "   docker pull $IMAGE_NAME"
        echo "   # Alterar tag no docker-compose.yml"
        echo "   docker-compose up -d aptor"
    else
        echo "❌ Erro no push!"
        exit 1
    fi
else
    echo "❌ Erro no build!"
    exit 1
fi

echo ""
echo "🎉 Build e push concluídos com sucesso!"
echo "📦 Imagem: $IMAGE_NAME"
echo "🏗️  Pronta para deploy Ubuntu!"
