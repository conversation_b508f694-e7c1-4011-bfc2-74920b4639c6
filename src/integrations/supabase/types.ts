export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.12 (cd3cf9e)"
  }
  public: {
    Tables: {
      agendamentos: {
        Row: {
          cliente_id: string | null
          created_at: string | null
          data_hora: string
          duracao: number | null
          google_event_id: string | null
          id: string
          observacoes: string | null
          profissional_id: string | null
          status: string | null
          webhook_enviado: boolean | null
        }
        Insert: {
          cliente_id?: string | null
          created_at?: string | null
          data_hora: string
          duracao?: number | null
          google_event_id?: string | null
          id?: string
          observacoes?: string | null
          profissional_id?: string | null
          status?: string | null
          webhook_enviado?: boolean | null
        }
        Update: {
          cliente_id?: string | null
          created_at?: string | null
          data_hora?: string
          duracao?: number | null
          google_event_id?: string | null
          id?: string
          observacoes?: string | null
          profissional_id?: string | null
          status?: string | null
          webhook_enviado?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "agendamentos_cliente_id_fkey"
            columns: ["cliente_id"]
            isOneToOne: false
            referencedRelation: "clientes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agendamentos_profissional_id_fkey"
            columns: ["profissional_id"]
            isOneToOne: false
            referencedRelation: "profissionais"
            referencedColumns: ["id"]
          },
        ]
      }
      clientes: {
        Row: {
          cpf: string | null
          created_at: string
          data_nascimento: string | null
          data_ultimo_agendamento: string | null
          email: string | null
          endereco: string | null
          id: string
          nome: string | null
          numero_whatsapp: string
          presenca_status: string | null
          status_agendamento:
            | Database["public"]["Enums"]["status_agendamento"]
            | null
        }
        Insert: {
          cpf?: string | null
          created_at?: string
          data_nascimento?: string | null
          data_ultimo_agendamento?: string | null
          email?: string | null
          endereco?: string | null
          id?: string
          nome?: string | null
          numero_whatsapp: string
          presenca_status?: string | null
          status_agendamento?:
            | Database["public"]["Enums"]["status_agendamento"]
            | null
        }
        Update: {
          cpf?: string | null
          created_at?: string
          data_nascimento?: string | null
          data_ultimo_agendamento?: string | null
          email?: string | null
          endereco?: string | null
          id?: string
          nome?: string | null
          numero_whatsapp?: string
          presenca_status?: string | null
          status_agendamento?:
            | Database["public"]["Enums"]["status_agendamento"]
            | null
        }
        Relationships: []
      }
      horarios_trabalho: {
        Row: {
          ativo: boolean | null
          bloqueio_event_id: string | null
          created_at: string | null
          dia_semana: number
          hora_fim: string
          hora_inicio: string
          id: string
          profissional_id: string | null
        }
        Insert: {
          ativo?: boolean | null
          bloqueio_event_id?: string | null
          created_at?: string | null
          dia_semana: number
          hora_fim: string
          hora_inicio: string
          id?: string
          profissional_id?: string | null
        }
        Update: {
          ativo?: boolean | null
          bloqueio_event_id?: string | null
          created_at?: string | null
          dia_semana?: number
          hora_fim?: string
          hora_inicio?: string
          id?: string
          profissional_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "horarios_trabalho_profissional_id_fkey"
            columns: ["profissional_id"]
            isOneToOne: false
            referencedRelation: "profissionais"
            referencedColumns: ["id"]
          },
        ]
      }
      profissionais: {
        Row: {
          conselho_sigla: string | null
          created_at: string | null
          crm: string | null
          duracao_consulta: number | null
          email: string
          especialidade: string
          id: string
          informacoes_importantes: string | null
          nome: string
          redes_sociais: Json | null
          registro_conselho: string | null
          telefone: string | null
        }
        Insert: {
          conselho_sigla?: string | null
          created_at?: string | null
          crm?: string | null
          duracao_consulta?: number | null
          email: string
          especialidade: string
          id?: string
          informacoes_importantes?: string | null
          nome: string
          redes_sociais?: Json | null
          registro_conselho?: string | null
          telefone?: string | null
        }
        Update: {
          conselho_sigla?: string | null
          created_at?: string | null
          crm?: string | null
          duracao_consulta?: number | null
          email?: string
          especialidade?: string
          id?: string
          informacoes_importantes?: string | null
          nome?: string
          redes_sociais?: Json | null
          registro_conselho?: string | null
          telefone?: string | null
        }
        Relationships: []
      }
      webhooks_log: {
        Row: {
          agendamento_id: string | null
          created_at: string | null
          id: string
          payload: Json | null
          status_resposta: number | null
          tipo_evento: string
        }
        Insert: {
          agendamento_id?: string | null
          created_at?: string | null
          id?: string
          payload?: Json | null
          status_resposta?: number | null
          tipo_evento: string
        }
        Update: {
          agendamento_id?: string | null
          created_at?: string | null
          id?: string
          payload?: Json | null
          status_resposta?: number | null
          tipo_evento?: string
        }
        Relationships: [
          {
            foreignKeyName: "webhooks_log_agendamento_id_fkey"
            columns: ["agendamento_id"]
            isOneToOne: false
            referencedRelation: "agendamentos"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      status_agendamento:
        | "pendente"
        | "confirmado"
        | "cancelado"
        | "concluído"
        | "ausente"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      status_agendamento: [
        "pendente",
        "confirmado",
        "cancelado",
        "concluído",
        "ausente",
      ],
    },
  },
} as const
