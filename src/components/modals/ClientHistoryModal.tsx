import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, User } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { agendamentoService, type Cliente } from "@/services/database";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface ClientHistoryModalProps {
  client: Cliente | null;
  isOpen: boolean;
  onClose: () => void;
}

const ClientHistoryModal = ({ client, isOpen, onClose }: ClientHistoryModalProps) => {
  // Fetch appointments for this client
  const { data: appointments = [], isLoading } = useQuery({
    queryKey: ['client-appointments', client?.id],
    queryFn: () => client ? agendamentoService.getByCliente(client.id) : Promise.resolve([]),
    enabled: !!client && isOpen
  });

  // Filter appointments from last 90 days
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

  const recentAppointments = appointments
    .filter(apt => new Date(apt.data_hora) >= ninetyDaysAgo)
    .sort((a, b) => new Date(b.data_hora).getTime() - new Date(a.data_hora).getTime());

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'agendado':
      case 'confirmado':
        return 'bg-blue-100 text-blue-800';
      case 'concluído':
        return 'bg-green-100 text-green-800';
      case 'cancelado':
        return 'bg-red-100 text-red-800';
      case 'ausente':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'agendado':
        return 'Agendado';
      case 'confirmado':
        return 'Confirmado';
      case 'concluído':
        return 'Concluído';
      case 'cancelado':
        return 'Cancelado';
      case 'ausente':
        return 'Ausente';
      default:
        return status;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-primary" />
            <span>Histórico de Atendimentos</span>
          </DialogTitle>
          <DialogDescription>
            {client ? `Últimos 90 dias - ${client.nome}` : "Carregando..."}
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          {isLoading ? (
            <div className="text-center py-8 text-muted-foreground">
              Carregando histórico...
            </div>
          ) : recentAppointments.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Nenhum atendimento encontrado nos últimos 90 dias</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium">
                  {recentAppointments.length} atendimento(s) encontrado(s)
                </h4>
                <Badge variant="outline">
                  Últimos 90 dias
                </Badge>
              </div>
              
              <div className="space-y-3">
                {recentAppointments.map((appointment, index) => (
                  <div 
                    key={appointment.id}
                    className="flex items-center justify-between p-4 bg-card rounded-lg border hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex flex-col items-center justify-center w-12 h-12 bg-primary/10 rounded-lg">
                        <Calendar className="h-4 w-4 text-primary" />
                        <span className="text-xs font-medium text-primary">
                          {format(new Date(appointment.data_hora), 'dd', { locale: ptBR })}
                        </span>
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="font-medium">
                            {format(new Date(appointment.data_hora), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
                          </span>
                          <Badge className={getStatusColor(appointment.status || 'agendado')}>
                            {getStatusText(appointment.status || 'agendado')}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>
                              {format(new Date(appointment.data_hora), 'HH:mm', { locale: ptBR })}
                            </span>
                          </div>
                          
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3" />
                            <span>{appointment.duracao || 30} min</span>
                          </div>
                        </div>
                        
                        {appointment.observacoes && (
                          <div className="mt-2 text-sm text-muted-foreground">
                            <strong>Observações:</strong> {appointment.observacoes}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ClientHistoryModal;
