import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface EmailNotificationRequest {
  to: string;
  clientName: string;
  professionalName: string;
  professionalEmail?: string;
  professionalPhone?: string;
  appointmentDate: string;
  appointmentTime: string;
  appointmentDuration?: number;
  appointmentNotes?: string;
  type: 'confirmation' | 'reminder' | 'cancellation' | 'no_show_alert';
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const {
      to,
      clientName,
      professionalName,
      professionalEmail,
      professionalPhone,
      appointmentDate,
      appointmentTime,
      appointmentDuration = 30,
      appointmentNotes,
      type
    }: EmailNotificationRequest = await req.json();

    console.log('Sending email notification:', { to, type, clientName });

    let subject = '';
    let html = '';

    switch (type) {
      case 'confirmation':
        subject = `Agendamento confirmado com ${professionalName}`;
        html = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9fafb;">
            <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <h1 style="color: #059669; margin-bottom: 20px;">✅ Agendamento Confirmado!</h1>
              <p>Olá, <strong>${clientName}</strong>!</p>
              <p>Seu agendamento foi confirmado com sucesso:</p>

              <div style="background-color: #f0f9ff; padding: 20px; border-radius: 6px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #0369a1;">Detalhes do Atendimento</h3>
                <ul style="list-style: none; padding: 0;">
                  <li style="margin: 8px 0;"><strong>👨‍⚕️ Profissional:</strong> ${professionalName}</li>
                  <li style="margin: 8px 0;"><strong>📅 Data:</strong> ${appointmentDate}</li>
                  <li style="margin: 8px 0;"><strong>🕐 Horário:</strong> ${appointmentTime}</li>
                  <li style="margin: 8px 0;"><strong>⏱️ Duração:</strong> ${appointmentDuration} minutos</li>
                  ${appointmentNotes ? `<li style="margin: 8px 0;"><strong>📝 Observações:</strong> ${appointmentNotes}</li>` : ''}
                </ul>
              </div>

              <div style="background-color: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <p style="margin: 0;"><strong>⚠️ Importante:</strong> Chegue 15 minutos antes do horário marcado.</p>
              </div>

              ${professionalPhone || professionalEmail ? `
                <div style="margin: 20px 0;">
                  <h4>📞 Contato do Profissional:</h4>
                  ${professionalPhone ? `<p>Telefone: ${professionalPhone}</p>` : ''}
                  ${professionalEmail ? `<p>Email: ${professionalEmail}</p>` : ''}
                </div>
              ` : ''}

              <p>Em caso de dúvidas ou necessidade de cancelamento, entre em contato conosco.</p>
              <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">
                Atenciosamente,<br>
                <strong>Equipe de Atendimento</strong>
              </p>
            </div>
          </div>
        `;
        break;
      case 'reminder':
        subject = `🔔 Lembrete: Agendamento com ${professionalName} amanhã`;
        html = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9fafb;">
            <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <h1 style="color: #f59e0b; margin-bottom: 20px;">🔔 Lembrete de Agendamento</h1>
              <p>Olá, <strong>${clientName}</strong>!</p>
              <p>Este é um lembrete do seu agendamento:</p>

              <div style="background-color: #fef3c7; padding: 20px; border-radius: 6px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #d97706;">📅 Agendamento para Amanhã</h3>
                <ul style="list-style: none; padding: 0;">
                  <li style="margin: 8px 0;"><strong>👨‍⚕️ Profissional:</strong> ${professionalName}</li>
                  <li style="margin: 8px 0;"><strong>📅 Data:</strong> ${appointmentDate}</li>
                  <li style="margin: 8px 0;"><strong>🕐 Horário:</strong> ${appointmentTime}</li>
                  <li style="margin: 8px 0;"><strong>⏱️ Duração:</strong> ${appointmentDuration} minutos</li>
                </ul>
              </div>

              <div style="background-color: #dbeafe; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <p style="margin: 0;"><strong>💡 Lembrete:</strong> Chegue 15 minutos antes do horário marcado.</p>
              </div>

              <p>Até amanhã!</p>
              <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">
                Atenciosamente,<br>
                <strong>Equipe de Atendimento</strong>
              </p>
            </div>
          </div>
        `;
        break;
      case 'cancellation':
        subject = `❌ Agendamento cancelado com ${professionalName}`;
        html = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9fafb;">
            <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <h1 style="color: #dc2626; margin-bottom: 20px;">❌ Agendamento Cancelado</h1>
              <p>Olá, <strong>${clientName}</strong>!</p>
              <p>Informamos que seu agendamento foi cancelado:</p>

              <div style="background-color: #fef2f2; padding: 20px; border-radius: 6px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #dc2626;">📅 Agendamento Cancelado</h3>
                <ul style="list-style: none; padding: 0;">
                  <li style="margin: 8px 0;"><strong>👨‍⚕️ Profissional:</strong> ${professionalName}</li>
                  <li style="margin: 8px 0;"><strong>📅 Data:</strong> ${appointmentDate}</li>
                  <li style="margin: 8px 0;"><strong>🕐 Horário:</strong> ${appointmentTime}</li>
                </ul>
              </div>

              <div style="background-color: #dbeafe; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <p style="margin: 0;"><strong>📞 Para reagendar:</strong> Entre em contato conosco pelos canais disponíveis.</p>
              </div>

              <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">
                Atenciosamente,<br>
                <strong>Equipe de Atendimento</strong>
              </p>
            </div>
          </div>
        `;
        break;

      case 'no_show_alert':
        subject = `⚠️ Cliente não compareceu - ${clientName}`;
        html = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9fafb;">
            <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <h1 style="color: #f59e0b; margin-bottom: 20px;">⚠️ Alerta de Ausência</h1>
              <p>Olá, <strong>${professionalName}</strong>!</p>
              <p>O cliente <strong>${clientName}</strong> não compareceu ao agendamento:</p>

              <div style="background-color: #fef3c7; padding: 20px; border-radius: 6px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #d97706;">📅 Detalhes do Agendamento</h3>
                <ul style="list-style: none; padding: 0;">
                  <li style="margin: 8px 0;"><strong>👤 Cliente:</strong> ${clientName}</li>
                  <li style="margin: 8px 0;"><strong>📅 Data:</strong> ${appointmentDate}</li>
                  <li style="margin: 8px 0;"><strong>🕐 Horário:</strong> ${appointmentTime}</li>
                </ul>
              </div>

              <p>O status foi automaticamente atualizado para "ausente" no sistema.</p>
              <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">
                Sistema de Agendamento Automático
              </p>
            </div>
          </div>
        `;
        break;
    }

    const emailResponse = await resend.emails.send({
      from: "Agendamentos <<EMAIL>>",
      to: [to],
      subject,
      html,
    });

    console.log("Email sent successfully:", emailResponse);

    return new Response(JSON.stringify(emailResponse), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  } catch (error: any) {
    console.error("Error in send-email-notification function:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);