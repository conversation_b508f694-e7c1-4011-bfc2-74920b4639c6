-- ===========================================
-- APTOR - SCRIPT 4: CRIAR SUPERADMIN INICIAL
-- Execute este SQL QUARTO no Supabase SQL Editor
-- IMPORTANTE: Execute apenas UMA VEZ!
-- ===========================================

-- ===========================================
-- CRIAR SUPERADMIN INICIAL
-- ===========================================

DO $$
DECLARE
    superadmin_id UUID;
    password_hash TEXT;
BEGIN
    -- Gerar hash da senha (ALTERE A SENHA AQUI!)
    -- <PERSON><PERSON> pad<PERSON>ão: "SuperAdmin@2025" (ALTERE APÓS PRIMEIRO LOGIN!)
    password_hash := crypt('SuperAdmin@2025', gen_salt('bf', 10));
    
    -- Inserir usuário superadmin
    INSERT INTO users (id, email, name, email_verified) 
    VALUES (
        uuid_generate_v4(),
        '<EMAIL>',
        'SuperAdmin Aptor',
        TRUE
    ) ON CONFLICT (email) DO UPDATE SET
        name = EXCLUDED.name,
        email_verified = EXCLUDED.email_verified,
        updated_at = NOW()
    RETURNING id INTO superadmin_id;
    
    -- Se não retornou ID (conflito resolvido com UPDATE), buscar o ID existente
    IF superadmin_id IS NULL THEN
        SELECT id INTO superadmin_id FROM users WHERE email = '<EMAIL>';
    END IF;
    
    -- Inserir perfil do superadmin
    INSERT INTO user_profiles (user_id, role, specialty, is_active, created_by)
    VALUES (superadmin_id, 'superadmin', 'Administração do Sistema', TRUE, superadmin_id)
    ON CONFLICT (user_id) DO UPDATE SET
        role = 'superadmin',
        specialty = 'Administração do Sistema',
        is_active = TRUE,
        updated_at = NOW(),
        updated_by = superadmin_id;
        
    -- Inserir conta com senha
    INSERT INTO accounts (user_id, account_id, provider_id, password)
    VALUES (
        superadmin_id,
        '<EMAIL>',
        'credential',
        password_hash
    ) ON CONFLICT (provider_id, account_id) DO UPDATE SET
        password = password_hash,
        updated_at = NOW();
    
    -- Log da criação
    INSERT INTO audit_logs (user_id, action, resource, resource_id, new_values)
    VALUES (
        superadmin_id,
        'CREATE_SUPERADMIN',
        'users',
        superadmin_id::text,
        jsonb_build_object(
            'email', '<EMAIL>',
            'role', 'superadmin',
            'created_at', NOW()
        )
    );
    
    RAISE NOTICE '===========================================';
    RAISE NOTICE 'SuperAdmin criado com sucesso!';
    RAISE NOTICE 'Email: <EMAIL>';
    RAISE NOTICE 'Senha: SuperAdmin@2025';
    RAISE NOTICE 'ID: %', superadmin_id;
    RAISE NOTICE '===========================================';
    RAISE NOTICE 'IMPORTANTE: ALTERE A SENHA APÓS O PRIMEIRO LOGIN!';
    RAISE NOTICE '===========================================';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Erro ao criar SuperAdmin: %', SQLERRM;
        RAISE;
END $$;

-- ===========================================
-- CRIAR USUÁRIOS DE TESTE (OPCIONAL)
-- Descomente apenas para ambiente de desenvolvimento
-- ===========================================

/*
DO $$
DECLARE
    prof_id UUID;
    password_hash TEXT;
BEGIN
    -- Senha para usuários de teste: "Teste@2025"
    password_hash := crypt('Teste@2025', gen_salt('bf', 10));
    
    -- Usuário profissional de teste
    INSERT INTO users (id, email, name, email_verified) 
    VALUES (
        uuid_generate_v4(),
        '<EMAIL>',
        'Profissional Teste',
        TRUE
    ) ON CONFLICT (email) DO NOTHING
    RETURNING id INTO prof_id;
    
    IF prof_id IS NOT NULL THEN
        -- Perfil do profissional
        INSERT INTO user_profiles (user_id, role, specialty, is_active)
        VALUES (prof_id, 'profissional', 'Clínica Geral', TRUE);
        
        -- Conta do profissional
        INSERT INTO accounts (user_id, account_id, provider_id, password)
        VALUES (prof_id, '<EMAIL>', 'credential', password_hash);
        
        RAISE NOTICE 'Usuário de teste criado: <EMAIL> / Teste@2025';
    END IF;
    
    -- Usuário inativo de teste
    INSERT INTO users (id, email, name, email_verified) 
    VALUES (
        uuid_generate_v4(),
        '<EMAIL>',
        'Usuário Inativo',
        TRUE
    ) ON CONFLICT (email) DO NOTHING
    RETURNING id INTO prof_id;
    
    IF prof_id IS NOT NULL THEN
        -- Perfil inativo
        INSERT INTO user_profiles (user_id, role, specialty, is_active, deactivated_reason)
        VALUES (prof_id, 'inactive', 'Teste', FALSE, 'Conta de teste desativada');
        
        -- Conta do usuário inativo
        INSERT INTO accounts (user_id, account_id, provider_id, password)
        VALUES (prof_id, '<EMAIL>', 'credential', password_hash);
        
        RAISE NOTICE 'Usuário inativo de teste criado: <EMAIL> / Teste@2025';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Erro ao criar usuários de teste: %', SQLERRM;
END $$;
*/

-- ===========================================
-- VERIFICAR CRIAÇÃO
-- ===========================================

-- Verificar se SuperAdmin foi criado corretamente
SELECT 
    u.id,
    u.email,
    u.name,
    u.email_verified,
    up.role,
    up.is_active,
    up.specialty,
    u.created_at
FROM users u
JOIN user_profiles up ON u.id = up.user_id
WHERE u.email = '<EMAIL>';

-- Verificar se conta foi criada
SELECT 
    a.user_id,
    a.account_id,
    a.provider_id,
    CASE 
        WHEN a.password IS NOT NULL THEN 'Senha configurada'
        ELSE 'Sem senha'
    END as password_status,
    a.created_at
FROM accounts a
JOIN users u ON a.user_id = u.id
WHERE u.email = '<EMAIL>';

-- Contar usuários por role
SELECT 
    up.role,
    up.is_active,
    COUNT(*) as total
FROM user_profiles up
GROUP BY up.role, up.is_active
ORDER BY up.role, up.is_active;

-- Mostrar resultado final
SELECT 
    'SuperAdmin criado com sucesso!' as status,
    '<EMAIL>' as email,
    'SuperAdmin@2025' as senha_inicial,
    'ALTERE A SENHA APÓS PRIMEIRO LOGIN!' as importante;
