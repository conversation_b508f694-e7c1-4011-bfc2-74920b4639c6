import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import availabilityService from "@/services/availabilityService";
import redisApiClient from "@/services/redisApiClient";

interface TestResult {
  step: string;
  status: 'running' | 'success' | 'error' | 'warning';
  data?: any;
  error?: string;
}

const TestRedisSystem = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);

  const updateResult = (index: number, update: Partial<TestResult>) => {
    setResults(prev => prev.map((result, i) => 
      i === index ? { ...result, ...update } : result
    ));
  };

  const addResult = (result: TestResult) => {
    setResults(prev => [...prev, result]);
    return results.length; // Return the index for updates
  };

  const runTests = async () => {
    setIsRunning(true);
    setResults([]);

    const profissionalId = 'test-prof-123';
    const date = '2025-08-21';
    const time = '10:00';
    const sessionId = `test-session-${Date.now()}`;

    try {
      // 1. Health Check
      let index = addResult({ step: '1. Health Check', status: 'running' });
      try {
        const health = await availabilityService.healthCheck();
        updateResult(index, { 
          status: health.redis && health.database ? 'success' : 'warning',
          data: health 
        });
      } catch (error) {
        updateResult(index, { 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }

      // 2. Get Available Slots
      index = addResult({ step: '2. Get Available Slots', status: 'running' });
      try {
        const slots = await availabilityService.getAvailableSlots(profissionalId, date);
        updateResult(index, { 
          status: 'success',
          data: { count: slots.length, sample: slots.slice(0, 3) }
        });
      } catch (error) {
        updateResult(index, { 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }

      // 3. Lock Time Slot
      index = addResult({ step: '3. Lock Time Slot', status: 'running' });
      try {
        const locked = await availabilityService.lockTimeSlot(profissionalId, date, time, sessionId);
        updateResult(index, { 
          status: locked ? 'success' : 'error',
          data: { locked, sessionId }
        });
      } catch (error) {
        updateResult(index, { 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }

      // 4. Check Lock Status
      index = addResult({ step: '4. Check Lock Status', status: 'running' });
      try {
        const lockStatus = await availabilityService.isTimeSlotLocked(profissionalId, date, time);
        updateResult(index, { 
          status: 'success',
          data: lockStatus
        });
      } catch (error) {
        updateResult(index, { 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }

      // 5. Test Double Lock (should fail)
      index = addResult({ step: '5. Test Double Lock', status: 'running' });
      try {
        const doubleLock = await availabilityService.lockTimeSlot(profissionalId, date, time, 'different-session');
        updateResult(index, { 
          status: !doubleLock ? 'success' : 'error',
          data: { shouldFail: true, actualResult: doubleLock }
        });
      } catch (error) {
        updateResult(index, { 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }

      // 6. Unlock
      index = addResult({ step: '6. Unlock Time Slot', status: 'running' });
      try {
        const unlocked = await availabilityService.unlockTimeSlot(profissionalId, date, time, sessionId);
        updateResult(index, { 
          status: unlocked ? 'success' : 'error',
          data: { unlocked }
        });
      } catch (error) {
        updateResult(index, { 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }

      // 7. Cache Invalidation
      index = addResult({ step: '7. Cache Invalidation', status: 'running' });
      try {
        await availabilityService.invalidateCache(profissionalId, date);
        updateResult(index, { 
          status: 'success',
          data: { invalidated: true }
        });
      } catch (error) {
        updateResult(index, { 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }

      // 8. Metrics
      index = addResult({ step: '8. Get Metrics', status: 'running' });
      try {
        const metrics = await availabilityService.getMetrics();
        updateResult(index, { 
          status: 'success',
          data: metrics
        });
      } catch (error) {
        updateResult(index, { 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }

      // 9. Redis Connection Test
      index = addResult({ step: '9. Redis Connection', status: 'running' });
      try {
        const health = await redisApiClient.healthCheck();
        updateResult(index, {
          status: health.redis ? 'success' : 'error',
          data: { connected: health.redis, database: health.database }
        });
      } catch (error) {
        updateResult(index, {
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }

    } catch (error) {
      console.error('Test suite failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    const variants = {
      running: 'default',
      success: 'default',
      error: 'destructive',
      warning: 'secondary'
    } as const;

    return (
      <Badge variant={variants[status]} className="ml-2">
        {status.toUpperCase()}
      </Badge>
    );
  };

  const successCount = results.filter(r => r.status === 'success').length;
  const errorCount = results.filter(r => r.status === 'error').length;
  const warningCount = results.filter(r => r.status === 'warning').length;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-primary mb-4">
            🧪 Redis System Test Suite
          </h1>
          <p className="text-muted-foreground">
            Test the availability system with Redis cache and locks
          </p>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Controls</CardTitle>
            <CardDescription>
              Run comprehensive tests on the Redis-powered availability system
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={runTests} 
              disabled={isRunning}
              className="w-full"
            >
              {isRunning ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Running Tests...
                </>
              ) : (
                '🚀 Run All Tests'
              )}
            </Button>

            {results.length > 0 && (
              <div className="mt-4 p-4 bg-muted rounded-lg">
                <h3 className="font-semibold mb-2">Summary</h3>
                <div className="flex gap-4 text-sm">
                  <span className="text-green-600">✅ Success: {successCount}</span>
                  <span className="text-red-600">❌ Errors: {errorCount}</span>
                  <span className="text-yellow-600">⚠️ Warnings: {warningCount}</span>
                  <span className="text-muted-foreground">Total: {results.length}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {results.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {results.map((result, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      {getStatusIcon(result.status)}
                      <span className="ml-2 font-medium">{result.step}</span>
                      {getStatusBadge(result.status)}
                    </div>
                  </div>
                  
                  {result.error && (
                    <div className="text-red-600 text-sm mt-2 p-2 bg-red-50 rounded">
                      <strong>Error:</strong> {result.error}
                    </div>
                  )}
                  
                  {result.data && (
                    <div className="text-sm mt-2 p-2 bg-muted rounded">
                      <strong>Data:</strong>
                      <pre className="mt-1 text-xs overflow-x-auto">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default TestRedisSystem;
